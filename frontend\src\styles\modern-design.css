/* ===== 现代化设计系统 ===== */

:root {
  /* ===== 现代化颜色系统 ===== */
  
  /* 主色调 - 现代蓝色 */
  --primary-color: #3b82f6;
  --primary-light: #60a5fa;
  --primary-dark: #2563eb;
  --primary-bg: #eff6ff;
  
  /* 中性色系 - 现代灰色 */
  --gray-50: #f9fafb;
  --gray-100: #f3f4f6;
  --gray-200: #e5e7eb;
  --gray-300: #d1d5db;
  --gray-400: #9ca3af;
  --gray-500: #6b7280;
  --gray-600: #4b5563;
  --gray-700: #374151;
  --gray-800: #1f2937;
  --gray-900: #111827;
  
  /* 功能色 */
  --success-color: #10b981;
  --success-bg: #ecfdf5;
  --warning-color: #f59e0b;
  --warning-bg: #fffbeb;
  --danger-color: #ef4444;
  --danger-bg: #fef2f2;
  
  /* 背景色系 */
  --bg-primary: #ffffff;
  --bg-secondary: #f8fafc;
  --bg-tertiary: #f1f5f9;
  
  /* 文字颜色 */
  --text-primary: #1e293b;
  --text-secondary: #64748b;
  --text-tertiary: #94a3b8;
  
  /* 边框和阴影 */
  --border-color: #e2e8f0;
  --border-light: #f1f5f9;
  --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  
  /* 间距系统 */
  --space-1: 0.25rem;
  --space-2: 0.5rem;
  --space-3: 0.75rem;
  --space-4: 1rem;
  --space-5: 1.25rem;
  --space-6: 1.5rem;
  --space-8: 2rem;
  --space-10: 2.5rem;
  --space-12: 3rem;
  
  /* 圆角系统 */
  --radius-sm: 0.375rem;
  --radius-md: 0.5rem;
  --radius-lg: 0.75rem;
  --radius-xl: 1rem;
  
  /* 字体系统 */
  --font-sans: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
  --font-size-xs: 0.75rem;
  --font-size-sm: 0.875rem;
  --font-size-base: 1rem;
  --font-size-lg: 1.125rem;
  --font-size-xl: 1.25rem;
  --font-size-2xl: 1.5rem;
  --font-size-3xl: 1.875rem;
  
  /* 过渡动画 */
  --transition-fast: 150ms ease-in-out;
  --transition-base: 200ms ease-in-out;
  --transition-slow: 300ms ease-in-out;
}

/* ===== 全局重置 ===== */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

html {
  font-family: var(--font-sans);
  font-size: 14px;
  line-height: 1.6;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

body {
  background-color: var(--bg-secondary);
  color: var(--text-primary);
  min-height: 100vh;
}

#app {
  width: 100%;
  min-height: 100vh;
}

/* ===== 现代化布局系统 ===== */

/* 主布局容器 */
.modern-layout {
  display: flex;
  height: 100vh;
  background-color: var(--bg-secondary);
  overflow: hidden;
  position: relative;
}

/* 移动端布局适配 */
@media (max-width: 768px) {
  .modern-layout {
    flex-direction: column;
  }
}

/* 现代化侧边栏 */
.modern-sidebar {
  width: 280px;
  background: linear-gradient(180deg, #1e293b 0%, #334155 100%);
  color: white;
  flex-shrink: 0;
  display: flex;
  flex-direction: column;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
  border-right: 1px solid rgba(255, 255, 255, 0.1);
  transition: transform 0.3s ease;
}

/* 移动端侧边栏 */
@media (max-width: 768px) {
  .modern-sidebar {
    position: fixed;
    top: 0;
    left: 0;
    height: 100vh;
    width: 280px;
    z-index: 1000;
    transform: translateX(-100%);
    box-shadow: 2px 0 8px rgba(0, 0, 0, 0.3);
  }

  .modern-sidebar.mobile-open {
    transform: translateX(0);
  }
}

.sidebar-brand {
  padding: var(--space-6);
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  text-align: center;
}

.sidebar-brand h1 {
  font-size: var(--font-size-xl);
  font-weight: 700;
  margin-bottom: var(--space-2);
  background: linear-gradient(135deg, #60a5fa, #a78bfa);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.sidebar-brand .version {
  font-size: var(--font-size-xs);
  color: var(--gray-400);
  font-weight: 500;
}

/* 导航菜单 */
.modern-nav {
  flex: 1;
  padding: var(--space-4);
}

.nav-item {
  display: flex;
  align-items: center;
  padding: var(--space-4) var(--space-5);
  margin-bottom: var(--space-3);
  border-radius: var(--radius-xl);
  color: rgba(255, 255, 255, 0.7);
  text-decoration: none;
  font-weight: 600;
  font-size: var(--font-size-sm);
  transition: all var(--transition-base);
  cursor: pointer;
  position: relative;
  overflow: hidden;
}

.nav-item::before {
  content: '';
  position: absolute;
  left: 0;
  top: 0;
  width: 4px;
  height: 100%;
  background: var(--primary-color);
  transform: scaleY(0);
  transition: transform var(--transition-base);
}

.nav-item:hover {
  background: rgba(255, 255, 255, 0.08);
  color: white;
  transform: translateX(8px);
}

.nav-item:hover::before {
  transform: scaleY(1);
}

.nav-item.active {
  background: linear-gradient(135deg, rgba(59, 130, 246, 0.15), rgba(96, 165, 250, 0.1));
  color: white;
  border: 1px solid rgba(59, 130, 246, 0.3);
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.2);
}

.nav-item.active::before {
  transform: scaleY(1);
}

.nav-item i {
  width: 20px;
  margin-right: var(--space-3);
  font-size: var(--font-size-lg);
}

/* 主内容区域 */
.modern-main {
  flex: 1;
  display: flex;
  flex-direction: column;
  background-color: var(--bg-secondary);
  height: 100vh;
  overflow: hidden;
}

/* 移动端主内容区域 */
@media (max-width: 768px) {
  .modern-main {
    width: 100%;
    margin-left: 0;
  }
}

/* 顶部导航栏 */
.modern-header {
  height: 80px;
  background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
  border-bottom: 1px solid rgba(226, 232, 240, 0.8);
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 var(--space-10);
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05), 0 1px 2px rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(8px);
}

/* 移动端头部 */
@media (max-width: 768px) {
  .modern-header {
    padding: 0 var(--space-4);
    padding-left: 70px; /* 为菜单按钮留出空间 */
    height: 60px;
  }
}

.header-title {
  font-size: var(--font-size-2xl);
  font-weight: 700;
  color: var(--text-primary);
  margin: 0;
}

.header-actions {
  display: flex;
  align-items: center;
  gap: var(--space-4);
}

.user-info {
  display: flex;
  align-items: center;
  gap: var(--space-3);
  padding: var(--space-2) var(--space-4);
  background-color: var(--bg-tertiary);
  border-radius: var(--radius-lg);
}

.user-avatar {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background: linear-gradient(135deg, #3b82f6, #1d4ed8);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-weight: 700;
  font-size: var(--font-size-base);
  box-shadow: 0 4px 8px rgba(59, 130, 246, 0.3);
  border: 2px solid rgba(255, 255, 255, 0.9);
}

.user-name {
  font-weight: 600;
  color: var(--text-primary);
  font-size: var(--font-size-sm);
}

/* 内容区域 */
.modern-content {
  flex: 1;
  padding: var(--space-6);
  overflow-y: auto;
  height: calc(100vh - 80px); /* 减去header高度 */
  display: flex;
  flex-direction: column;
}

/* 移动端内容区域 */
@media (max-width: 768px) {
  .modern-content {
    padding: var(--space-3);
    height: calc(100vh - 60px); /* 减去移动端header高度 */
  }
}

/* ===== 现代化组件样式 ===== */

/* 卡片组件 */
.modern-card {
  background-color: var(--bg-primary);
  border-radius: var(--radius-xl);
  box-shadow: var(--shadow-sm);
  border: 1px solid var(--border-light);
  overflow: hidden;
  margin-bottom: var(--space-4);
  transition: all var(--transition-base);
  padding: var(--space-6);
}

.modern-card:hover {
  box-shadow: var(--shadow-md);
  transform: translateY(-2px);
}

.card-header {
  padding: var(--space-6);
  border-bottom: 1px solid var(--border-light);
  background: linear-gradient(135deg, var(--bg-primary) 0%, var(--bg-tertiary) 100%);
}

.card-title {
  font-size: var(--font-size-lg);
  font-weight: 700;
  color: var(--text-primary);
  margin: 0;
  display: flex;
  align-items: center;
  gap: var(--space-3);
}

.card-title i {
  color: var(--primary-color);
}

.card-content {
  padding: var(--space-6);
}

/* 统计卡片 */
.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: var(--space-4);
  margin-bottom: var(--space-6);
}

/* 移动端统计卡片 */
@media (max-width: 768px) {
  .stats-grid {
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: var(--space-3);
    margin-bottom: var(--space-4);
  }
}

.stat-card {
  background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
  border-radius: var(--radius-lg);
  padding: var(--space-4);
  border: 1px solid rgba(226, 232, 240, 0.8);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.04), 0 1px 2px rgba(0, 0, 0, 0.08);
  transition: all var(--transition-slow);
  position: relative;
  overflow: hidden;
  min-height: 90px;
}

.stat-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, var(--primary-color), var(--primary-light));
  opacity: 0;
  transition: opacity var(--transition-base);
}

.stat-card:hover {
  transform: translateY(-4px) scale(1.01);
  box-shadow: 0 8px 15px rgba(0, 0, 0, 0.08), 0 4px 6px rgba(0, 0, 0, 0.04);
}

.stat-card:hover::before {
  opacity: 1;
}

.stat-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: var(--space-6);
}

.stat-label {
  font-size: var(--font-size-sm);
  color: var(--text-secondary);
  font-weight: 700;
  text-transform: uppercase;
  letter-spacing: 0.1em;
}

.stat-icon {
  width: 56px;
  height: 56px;
  border-radius: var(--radius-xl);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 26px;
  color: #ffffff !important;
  box-shadow: 0 8px 16px rgba(0, 0, 0, 0.15);
  position: relative;
}

.stat-icon i {
  color: #ffffff !important;
  font-weight: 900;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.stat-icon::before {
  content: '';
  position: absolute;
  inset: 0;
  border-radius: var(--radius-xl);
  background: linear-gradient(45deg, rgba(255,255,255,0.2), transparent);
  opacity: 0;
  transition: opacity var(--transition-base);
}

.stat-card:hover .stat-icon::before {
  opacity: 1;
}

.stat-icon.primary {
  background: linear-gradient(135deg, #1e40af, #1e3a8a);
  box-shadow: 0 8px 16px rgba(30, 64, 175, 0.5);
}
.stat-icon.success {
  background: linear-gradient(135deg, #047857, #065f46);
  box-shadow: 0 8px 16px rgba(4, 120, 87, 0.5);
}
.stat-icon.warning {
  background: linear-gradient(135deg, #b45309, #92400e);
  box-shadow: 0 8px 16px rgba(180, 83, 9, 0.5);
}
.stat-icon.danger {
  background: linear-gradient(135deg, #b91c1c, #991b1b);
  box-shadow: 0 8px 16px rgba(185, 28, 28, 0.5);
}

.stat-value {
  font-size: 2.5rem;
  font-weight: 900;
  color: var(--text-primary);
  line-height: 1;
  margin-top: var(--space-2);
}

/* 现代化搜索框 */
.modern-search {
  position: relative;
  display: flex;
  align-items: center;
  background: var(--surface-color);
  border: 1px solid var(--border-light);
  border-radius: var(--radius-lg);
  padding: 0 var(--space-3);
  transition: all var(--transition-base);
  min-width: 280px;
}

.modern-search:focus-within {
  border-color: var(--primary-color);
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.modern-search i {
  color: var(--text-secondary);
  margin-right: var(--space-2);
  font-size: 14px;
}

.modern-search input {
  border: none;
  outline: none;
  background: transparent;
  flex: 1;
  padding: var(--space-3) 0;
  color: var(--text-primary);
  font-size: var(--font-size-sm);
}

.modern-search input::placeholder {
  color: var(--text-secondary);
}

/* 现代化表格样式 */
.modern-table-container {
  background: var(--surface-color);
  border-radius: var(--radius-xl);
  overflow: hidden;
  box-shadow: var(--shadow-sm);
  border: 1px solid var(--border-light);
  flex: 1;
  display: flex;
  flex-direction: column;
}

/* 移动端表格样式 */
@media (max-width: 768px) {
  .modern-table-container {
    border-radius: var(--radius-lg);
    overflow-x: auto;
  }

  .modern-table-container .el-table {
    min-width: 600px; /* 确保表格内容不会被压缩 */
  }
}

.modern-table-container .el-table {
  background: transparent;
}

.modern-table-container .el-table__header {
  background: linear-gradient(135deg, #f8fafc, #f1f5f9);
}

.modern-table-container .el-table__header th {
  background: transparent;
  border-bottom: 2px solid var(--border-light);
  color: var(--text-primary);
  font-weight: 700;
  font-size: var(--font-size-sm);
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.modern-table-container .el-table__row {
  transition: all var(--transition-base);
}

.modern-table-container .el-table__row:hover {
  background: rgba(59, 130, 246, 0.05);
}

.modern-table-container .el-table td {
  border-bottom: 1px solid var(--border-light);
  padding: var(--space-4) var(--space-3);
}

/* 现代化按钮 */
.modern-btn {
  display: inline-flex;
  align-items: center;
  gap: var(--space-3);
  padding: var(--space-4) var(--space-6);
  border-radius: var(--radius-xl);
  font-weight: 700;
  font-size: var(--font-size-sm);
  text-decoration: none;
  border: none;
  cursor: pointer;
  transition: all var(--transition-slow);
  position: relative;
  overflow: hidden;
  letter-spacing: 0.025em;
}

.modern-btn:before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent);
  transition: left var(--transition-slow);
}

.modern-btn:hover:before {
  left: 100%;
}

/* 快速操作按钮特殊样式 */
.quick-action-btn {
  width: 100%;
  justify-content: flex-start;
  padding: var(--space-4) var(--space-5);
  margin-bottom: var(--space-3);
  background: linear-gradient(135deg, #f8fafc, #e2e8f0);
  color: var(--text-primary);
  border: 1px solid rgba(226, 232, 240, 0.8);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
  font-weight: 600;
}

.quick-action-btn:hover {
  background: linear-gradient(135deg, #e2e8f0, #cbd5e1);
  transform: translateY(-2px) translateX(4px);
  box-shadow: 0 8px 16px rgba(0, 0, 0, 0.1);
  border-color: var(--primary-color);
}

.quick-action-btn i {
  width: 20px;
  color: var(--primary-color) !important;
}

/* 按钮变体 */
.modern-btn.primary {
  background: linear-gradient(135deg, var(--primary-color), #1e40af);
  color: white;
  border: 1px solid var(--primary-color);
}

.modern-btn.primary i {
  color: white !important;
}

.modern-btn.primary:hover {
  background: linear-gradient(135deg, #1e40af, #1e3a8a);
  transform: translateY(-2px);
  box-shadow: 0 8px 16px rgba(59, 130, 246, 0.3);
}

.modern-btn.secondary {
  background: linear-gradient(135deg, #6b7280, #4b5563);
  color: white;
  border: 1px solid #6b7280;
}

.modern-btn.secondary i {
  color: white !important;
}

.modern-btn.secondary:hover {
  background: linear-gradient(135deg, #4b5563, #374151);
  transform: translateY(-2px);
  box-shadow: 0 8px 16px rgba(107, 114, 128, 0.3);
}

.modern-btn.success {
  background: linear-gradient(135deg, #10b981, #047857);
  color: white !important;
  border: 1px solid #10b981;
}

.modern-btn.success i {
  color: white !important;
}

.modern-btn.success:hover {
  background: linear-gradient(135deg, #047857, #065f46);
  transform: translateY(-2px);
  box-shadow: 0 8px 16px rgba(16, 185, 129, 0.3);
}

.modern-btn.warning {
  background: linear-gradient(135deg, #f59e0b, #d97706);
  color: white;
  border: 1px solid #f59e0b;
}

.modern-btn.warning i {
  color: white !important;
}

.modern-btn.warning:hover {
  background: linear-gradient(135deg, #d97706, #b45309);
  transform: translateY(-2px);
  box-shadow: 0 8px 16px rgba(245, 158, 11, 0.3);
}

.modern-btn.danger {
  background: linear-gradient(135deg, #ef4444, #dc2626);
  color: white;
  border: 1px solid #ef4444;
}

.modern-btn.danger i {
  color: white !important;
}

.modern-btn.danger:hover {
  background: linear-gradient(135deg, #dc2626, #b91c1c);
  transform: translateY(-2px);
  box-shadow: 0 8px 16px rgba(239, 68, 68, 0.3);
}

.modern-btn.small {
  padding: var(--space-2) var(--space-3);
  font-size: 12px;
  border-radius: var(--radius-md);
}

/* 移动端菜单按钮 */
.mobile-menu-btn {
  display: none;
  position: fixed;
  top: 15px;
  left: 15px;
  width: 40px;
  height: 40px;
  background: var(--primary-color);
  color: white;
  border-radius: 50%;
  border: none;
  z-index: 1001;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
  cursor: pointer;
  align-items: center;
  justify-content: center;
  font-size: 18px;
}

/* 移动端遮罩层 */
.mobile-overlay {
  display: none;
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.5);
  z-index: 999;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.mobile-overlay.active {
  opacity: 1;
}

@media (max-width: 768px) {
  .mobile-menu-btn {
    display: flex;
  }

  .mobile-overlay.show {
    display: block;
  }
}

/* 页面管理样式 - 确保内容适应屏幕 */
.user-management,
.device-management,
.mqtt-management,
.logs-management {
  height: 100%;
  display: flex;
  flex-direction: column;
  overflow-y: auto; /* 改为允许垂直滚动 */
  gap: 16px;
  padding: 0;
}

.user-management .modern-card:last-child,
.device-management .modern-card:last-child,
.mqtt-management .modern-card:last-child,
.logs-management .modern-card:last-child {
  flex: 1;
  display: flex;
  flex-direction: column;
  margin-bottom: 0;
}

.user-management .modern-table-container,
.device-management .modern-table-container,
.mqtt-management .modern-table-container,
.logs-management .modern-table-container {
  flex: 1;
  overflow-y: auto; /* 改为允许垂直滚动 */
}

.user-management .el-table,
.device-management .el-table,
.mqtt-management .el-table,
.logs-management .el-table {
  height: 100%;
}

/* 设备管理页面特定样式 */
.device-management {
  background: #f8fafc;
}

.device-management .device-toolbar {
  flex-shrink: 0;
  margin-bottom: 0;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
}

.device-management .card-content {
  padding: 0;
  flex: 1;
  display: flex;
  flex-direction: column;
}

.device-management .modern-table-container {
  border-radius: 0 0 8px 8px;
  border-top: none;
}

.btn-primary {
  background: linear-gradient(135deg, #3b82f6, #1d4ed8);
  color: white;
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.4);
  border: none;
}

.btn-primary:hover {
  transform: translateY(-3px);
  box-shadow: 0 8px 20px rgba(59, 130, 246, 0.5);
  background: linear-gradient(135deg, #2563eb, #1e40af);
}

.btn-secondary {
  background: linear-gradient(135deg, #f8fafc, #e2e8f0);
  color: var(--text-primary);
  border: 1px solid rgba(226, 232, 240, 0.8);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.btn-secondary:hover {
  background: linear-gradient(135deg, #e2e8f0, #cbd5e1);
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.btn-danger {
  background: linear-gradient(135deg, #ef4444, #dc2626);
  color: white;
  box-shadow: 0 4px 12px rgba(239, 68, 68, 0.4);
  border: none;
}

.btn-danger:hover {
  transform: translateY(-3px);
  box-shadow: 0 8px 20px rgba(239, 68, 68, 0.5);
  background: linear-gradient(135deg, #dc2626, #b91c1c);
}
