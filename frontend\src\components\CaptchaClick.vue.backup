<template>
  <div class="app-container">
    <!-- 页面标题 (仅在未登录时显示) -->
    <div v-if="!isLoggedIn" class="page-header">
      <h1 class="page-title">
        <i class="el-icon-monitor"></i>
        终端管理
      </h1>
      <p class="page-subtitle">ipcManagement</p>
    </div>

    <!-- 登录区域 -->
    <div v-if="!isLoggedIn" class="login-section">
      <div class="login-card">
        <div class="login-header">
          <h2>用户登录</h2>
          <p>请输入您的账号信息进行登录</p>
        </div>
        <el-form :model="loginForm" class="login-form" size="large">
          <el-form-item>
            <el-input 
              v-model="loginForm.username" 
              placeholder="请输入用户名"
              prefix-icon="el-icon-user"
              clearable
            />
          </el-form-item>
          <el-form-item>
            <el-input 
              v-model="loginForm.password" 
              type="password" 
              placeholder="请输入密码"
              prefix-icon="el-icon-lock"
              clearable
              show-password
            />
          </el-form-item>
          <el-form-item>
            <el-button 
              type="primary" 
              @click="handleLogin" 
              :loading="loginLoading"
              class="login-button"
            >
              {{ loginLoading ? '登录中...' : '登录' }}
            </el-button>
          </el-form-item>
        </el-form>
      </div>
    </div>
      
    <!-- 后台管理页面 -->
    <div v-if="isLoggedIn" class="admin-layout">
        <!-- 侧边栏 -->
        <div class="sidebar">
          <div class="sidebar-header">
            <h3>终端管理</h3>
            <span class="version">v1.0</span>
          </div>
          <el-menu
            :default-active="activeMenu"
            class="sidebar-menu"
            background-color="#2c3e50"
            text-color="#ecf0f1"
            active-text-color="#3498db"
            @select="handleMenuSelect"
          >
            <el-menu-item index="dashboard">
              <i class="el-icon-monitor"></i>
              <span>仪表盘</span>
            </el-menu-item>
            <el-menu-item index="captcha">
              <i class="el-icon-view"></i>
              <span>验证码测试</span>
            </el-menu-item>
            <el-menu-item index="users">
              <i class="el-icon-user"></i>
              <span>用户管理</span>
            </el-menu-item>
            <el-menu-item index="mqtt">
              <i class="el-icon-connection"></i>
              <span>MQTT服务器</span>
            </el-menu-item>
            <el-menu-item index="logs">
              <i class="el-icon-document"></i>
              <span>操作日志</span>
            </el-menu-item>
            <el-menu-item index="settings">
              <i class="el-icon-setting"></i>
              <span>系统设置</span>
            </el-menu-item>
          </el-menu>
        </div>

        <!-- 主内容区域 -->
        <div class="main-content">
          <!-- 顶部导航 -->
          <div class="header">
            <div class="header-left">
              <h2>{{ getMenuTitle(activeMenu) }}</h2>
            </div>
            <div class="header-right">
              <div class="user-info">
                <span class="welcome-text">欢迎，{{ username }}</span>
                <el-button type="danger" size="small" @click="logout" class="logout-btn">
                  退出登录
                </el-button>
              </div>
            </div>
          </div>

          <!-- 内容区域 -->
          <div class="content-area">
            <!-- 仪表盘 -->
            <div v-if="activeMenu === 'dashboard'" class="dashboard">
              <div class="stats-grid">
                <div class="stat-card">
                  <div class="stat-icon">
                    <i class="el-icon-user"></i>
                  </div>
                  <div class="stat-info">
                    <h3>当前用户</h3>
                    <p>{{ username }}</p>
                  </div>
                </div>
                <div class="stat-card">
                  <div class="stat-icon">
                    <i class="el-icon-s-data"></i>
                  </div>
                  <div class="stat-info">
                    <h3>总用户数</h3>
                    <p>{{ totalUsers }}</p>
                  </div>
                </div>
                <div class="stat-card">
                  <div class="stat-icon">
                    <i class="el-icon-time"></i>
                  </div>
                  <div class="stat-info">
                    <h3>系统状态</h3>
                    <p>运行中</p>
                  </div>
                </div>
              </div>
            </div>

            <!-- 验证码测试 -->
            <div v-else-if="activeMenu === 'captcha'" class="captcha-test">
              <div class="captcha-container">
                <div v-if="loading" class="loading-area">
                  <i class="el-icon-loading"></i>
                  <span>加载中...</span>
                </div>
                <div v-else-if="img" class="captcha-area">
                  <div class="captcha-title">请按数字顺序点击红点（共{{ count }}个）</div>
                  <div class="captcha-image-wrapper">
                    <img :src="img" @click="onImgClick" ref="imgRef" class="captcha-img"/>
                    <template v-for="(p, i) in clicks" :key="i">
                      <span :style="pointStyle(p)" class="click-point">x</span>
                    </template>
                  </div>
                  <div class="captcha-actions">
                    <el-button type="primary" @click="submit" :disabled="clicks.length!==count">
                      提交验证 ({{ clicks.length }}/{{ count }})
                    </el-button>
                    <el-button @click="refresh">刷新</el-button>
                    <el-button @click="calibrate">校准坐标</el-button>
                  </div>
                  <div v-if="msg" class="captcha-message" :style="{color:msgColor}">{{ msg }}</div>
                </div>
                <div v-else class="no-captcha">
                  <el-button type="primary" @click="loadCaptcha">生成验证码</el-button>
                </div>
              </div>
            </div>

            <!-- 用户管理 -->
            <div v-else-if="activeMenu === 'users'" class="user-management">
              <div class="logs-header">
                <h2>用户管理</h2>
                <div class="logs-actions">
                  <input 
                    v-model="searchKeyword" 
                    @input="handleSearch"
                    placeholder="搜索用户名..."
                    class="search-input"
                  />
                  <button @click="showAddUserDialog" class="refresh-btn">
                    <i class="el-icon-plus"></i>
                    添加用户
                  </button>
                </div>
              </div>
              
              <div class="user-table-container">
                <el-table
                  :data="filteredUsers"
                  border
                  stripe
                  style="width: 100%"
                  v-loading="usersLoading"
                >
                  <el-table-column prop="id" label="ID" width="80" />
                  <el-table-column prop="username" label="用户名" />
                  <el-table-column prop="is_active" label="状态" width="100">
                    <template #default="scope">
                      <el-tag :type="scope.row.is_active ? 'success' : 'danger'">
                        {{ scope.row.is_active ? '启用' : '禁用' }}
                      </el-tag>
                    </template>
                  </el-table-column>
                  <el-table-column prop="created_at" label="创建时间" width="180">
                    <template #default="scope">
                      {{ formatDate(scope.row.created_at) }}
                    </template>
                  </el-table-column>
                  <el-table-column label="操作" width="200">
                    <template #default="scope">
                      <el-button size="mini" @click="editUser(scope.row)">
                        <i class="el-icon-edit"></i>
                        编辑
                      </el-button>
                      <el-button 
                        size="mini" 
                        type="danger" 
                        @click="deleteUser(scope.row)" 
                        :disabled="scope.row.username === username || scope.row.username === 'admin'"
                      >
                        <i class="el-icon-delete"></i>
                        删除
                      </el-button>
                    </template>
                  </el-table-column>
                </el-table>
              </div>
            </div>

            <!-- MQTT服务器管理 -->
            <div v-else-if="activeMenu === 'mqtt'" class="mqtt-management">
              <div class="logs-header">
                <h2>MQTT服务器</h2>
                <div class="logs-actions">
                  <button @click="loadMqttStats" class="refresh-btn">
                    <i class="el-icon-refresh"></i>
                    刷新状态
                  </button>
                </div>
              </div>

              <!-- MQTT统计信息 -->
              <div class="logs-stats">
                <div class="stat-item">
                  <span class="stat-label">服务器状态:</span>
                  <span :class="mqttStats.running ? 'stat-value text-success' : 'stat-value text-danger'">
                    {{ mqttStats.running ? '运行中' : '已停止' }}
                  </span>
                </div>
                <div class="stat-item">
                  <span class="stat-label">连接客户端:</span>
                  <span class="stat-value">{{ mqttStats.clients || 0 }}</span>
                </div>
                <div class="stat-item">
                  <span class="stat-label">订阅主题:</span>
                  <span class="stat-value">{{ mqttStats.topics || 0 }}</span>
                </div>
                <div class="stat-item">
                  <span class="stat-label">黑名单:</span>
                  <span class="stat-value">{{ mqttStats.blacklist_count || 0 }}</span>
                </div>
              </div>

              <!-- MQTT内容区域 -->
              <div class="mqtt-content">
              
              <el-tabs v-model="mqttActiveTab" type="card" class="mqtt-tabs">
                <el-tab-pane label="连接管理" name="clients">
                  <div class="mqtt-clients">
                    <h3>连接的客户端</h3>
                    <div class="client-list">
                      <div v-if="mqttStats.client_list && mqttStats.client_list.length > 0" class="client-table">
                        <el-table :data="mqttStats.client_list" border stripe>
                          <el-table-column prop="id" label="客户端ID" width="280" class-name="client-id-cell" />
                          <el-table-column prop="username" label="用户名" width="120" />
                          <el-table-column prop="ip" label="IP地址" width="140" />
                          <el-table-column prop="authenticated" label="认证状态" width="100">
                            <template #default="scope">
                              <el-tag :type="scope.row.authenticated ? 'success' : 'danger'">
                                {{ scope.row.authenticated ? '已认证' : '未认证' }}
                              </el-tag>
                            </template>
                          </el-table-column>
                          <el-table-column prop="connected_at" label="连接时间" min-width="180" />
                        </el-table>
                      </div>
                      <div v-else class="no-clients">
                        <i class="el-icon-info"></i>
                        <span>暂无客户端连接</span>
                      </div>
                    </div>
                  </div>
                </el-tab-pane>
                
                <el-tab-pane label="安全管理" name="security">
                  <div class="security-section">
                    <h3>黑名单管理</h3>
                    <div class="blacklist-toolbar">
                      <el-button type="primary" @click="showAddBlacklistDialog">
                        <i class="el-icon-plus"></i>
                        添加黑名单
                      </el-button>
                    </div>
                    <div class="blacklist-table">
                      <el-table :data="mqttStats.blacklist" border stripe>
                        <el-table-column prop="ip" label="IP地址" width="140" />
                        <el-table-column prop="username" label="用户名" width="120" />
                        <el-table-column prop="reason" label="封禁原因" />
                        <el-table-column prop="added_at" label="添加时间" width="160">
                          <template #default="scope">
                            {{ formatDate(scope.row.added_at) }}
                          </template>
                        </el-table-column>
                        <el-table-column prop="expires_at" label="过期时间" width="160">
                          <template #default="scope">
                            {{ formatDate(scope.row.expires_at) }}
                          </template>
                        </el-table-column>
                        <el-table-column label="操作" width="100">
                          <template #default="scope">
                            <el-button size="mini" type="danger" @click="removeFromBlacklist(scope.row.ip)">
                              移除
                            </el-button>
                          </template>
                        </el-table-column>
                      </el-table>
                    </div>
                    
                    <h3>失败认证记录</h3>
                    <div class="failed-auth-table">
                      <el-table :data="mqttStats.failed_auth" border stripe>
                        <el-table-column prop="ip" label="IP地址" width="140" />
                        <el-table-column prop="username" label="用户名" width="120" />
                        <el-table-column prop="count" label="失败次数" width="100" />
                        <el-table-column prop="last_attempt" label="最后尝试" width="160">
                          <template #default="scope">
                            {{ formatDate(scope.row.last_attempt) }}
                          </template>
                        </el-table-column>
                        <el-table-column label="操作" width="100">
                          <template #default="scope">
                            <el-button size="mini" type="warning" @click="clearFailedAuth(scope.row.ip)">
                              清除
                            </el-button>
                          </template>
                        </el-table-column>
                      </el-table>
                    </div>
                  </div>
                </el-tab-pane>
                
                <el-tab-pane label="配置管理" name="config">
                  <div class="config-section">
                    <h3>MQTT服务器配置</h3>
                    
                    <!-- 认证说明 -->
                    <el-alert
                      title="MQTT认证说明"
                      type="info"
                      :closable="false"
                      style="margin-bottom: 20px;"
                    >
                      <template #default>
                        <p>MQTT服务器使用系统用户账号进行认证：</p>
                        <ul style="margin: 8px 0; padding-left: 20px;">
                          <li>MQTT客户端可使用"用户管理"中的账号密码连接</li>
                          <li>只有状态为"启用"的用户才能MQTT登录</li>
                          <li>用户名和密码与Web登录相同</li>
                        </ul>
                      </template>
                    </el-alert>
                    
                    <div class="config-form">
                      <el-form :model="mqttConfig" label-width="150px">
                        <el-form-item label="启用认证">
                          <el-switch v-model="mqttConfig.require_auth" />
                        </el-form-item>
                        <el-form-item label="允许匿名">
                          <el-switch v-model="mqttConfig.allow_anonymous" />
                        </el-form-item>
                        <el-form-item label="最大失败次数">
                          <el-input-number v-model="mqttConfig.max_failed_attempts" :min="1" :max="10" />
                        </el-form-item>
                        <el-form-item label="黑名单时长(小时)">
                          <el-input-number v-model="mqttConfig.blacklist_duration" :min="1" :max="168" />
                        </el-form-item>
                        <el-form-item label="清理间隔(分钟)">
                          <el-input-number v-model="mqttConfig.cleanup_interval" :min="1" :max="1440" />
                        </el-form-item>
                        <el-form-item>
                          <el-button type="primary" @click="updateMqttConfig" :loading="configLoading">
                            保存配置
                          </el-button>
                        </el-form-item>
                      </el-form>
                    </div>
                  </div>
                </el-tab-pane>
              </el-tabs>
              
              <div class="mqtt-topics">
                <h3>活跃主题</h3>
                <div class="topic-list">
                  <div v-if="mqttStats.topic_list && mqttStats.topic_list.length > 0" class="topic-items">
                    <div v-for="topic in mqttStats.topic_list" :key="topic" class="topic-item">
                      <i class="el-icon-message"></i>
                      <span>{{ topic }}</span>
                    </div>
                  </div>
                  <div v-else class="no-topics">
                    <i class="el-icon-info"></i>
                    <span>暂无活跃主题</span>
                  </div>
                </div>
              </div>
              
              <div class="mqtt-publish">
                <h3>发布消息</h3>
                <div class="publish-form">
                  <el-tabs v-model="activeTab" type="card">
                    <el-tab-pane label="广播消息" name="broadcast">
                      <div class="simple-form">
                        <div class="input-group">
                          <label>主题</label>
                          <input 
                            v-model="publishForm.topic" 
                            placeholder="请输入主题名称" 
                            class="form-input"
                          />
                        </div>
                        <div class="input-group">
                          <label>消息</label>
                          <input 
                            v-model="publishForm.message" 
                            placeholder="请输入消息内容" 
                            class="form-input"
                          />
                        </div>
                        <div class="button-group">
                          <button 
                            @click="publishMessage" 
                            :disabled="publishLoading"
                            class="publish-btn"
                          >
                            {{ publishLoading ? '发布中...' : '发布消息' }}
                          </button>
                        </div>
                      </div>
                    </el-tab-pane>
                    <el-tab-pane label="单发消息" name="unicast">
                      <div class="simple-form">
                        <div class="input-group">
                          <label>客户端</label>
                          <select v-model="sendForm.clientId" class="form-select">
                            <option value="">选择客户端</option>
                            <option 
                              v-for="client in mqttStats.client_list" 
                              :key="client.id" 
                              :value="client.id"
                            >
                              {{ client.id }} ({{ client.username }})
                            </option>
                          </select>
                        </div>
                        <div class="input-group">
                          <label>主题</label>
                          <input 
                            v-model="sendForm.topic" 
                            placeholder="请输入主题名称" 
                            class="form-input"
                          />
                        </div>
                        <div class="input-group">
                          <label>消息</label>
                          <input 
                            v-model="sendForm.message" 
                            placeholder="请输入消息内容" 
                            class="form-input"
                          />
                        </div>
                        <div class="button-group">
                          <button 
                            @click="sendToClient" 
                            :disabled="sendLoading"
                            class="send-btn"
                          >
                            {{ sendLoading ? '发送中...' : '发送消息' }}
                          </button>
                        </div>
                      </div>
                    </el-tab-pane>
                  </el-tabs>
              </div>
            </div>
            </div>

            <!-- 操作日志页面 -->
            <div v-else-if="activeMenu === 'logs'" class="logs-management">
              <div class="logs-header">
                <h2>操作日志</h2>
                <div class="logs-actions">
                  <button @click="refreshLogs" class="refresh-btn">
                    <i class="el-icon-refresh"></i>
                    刷新
                  </button>
                  <button @click="clearLogs" class="clear-btn">
                    <i class="el-icon-delete"></i>
                    清理日志
                  </button>
                </div>
              </div>
              
              <!-- 日志统计 -->
              <div class="logs-stats">
                <div class="stat-item">
                  <span class="stat-label">今日操作:</span>
                  <span class="stat-value">{{ logStats.today_count || 0 }}</span>
                </div>
                <div class="stat-item">
                  <span class="stat-label">成功率:</span>
                  <span class="stat-value">{{ (logStats.success_rate || 0).toFixed(1) }}%</span>
                </div>
              </div>
              
              <!-- 过滤器 -->
              <div class="logs-filters">
                <div class="filter-row">
                  <div class="filter-item">
                    <label>操作类型:</label>
                    <select v-model="logFilters.operation_type" class="filter-select">
                      <option value="">全部</option>
                      <option value="LOGIN">登录</option>
                      <option value="LOGIN_FAILED">登录失败</option>
                      <option value="CREATE_USER">创建用户</option>
                      <option value="UPDATE_USER">更新用户</option>
                      <option value="DELETE_USER">删除用户</option>
                    </select>
                  </div>
                  <div class="filter-item">
                    <label>模块:</label>
                    <select v-model="logFilters.module" class="filter-select">
                      <option value="">全部</option>
                      <option value="AUTH">认证</option>
                      <option value="USER_MANAGEMENT">用户管理</option>
                      <option value="MQTT">MQTT服务器</option>
                      <option value="SYSTEM">系统</option>
                    </select>
                  </div>
                  <div class="filter-item">
                    <label>搜索:</label>
                    <input 
                      v-model="logFilters.keyword" 
                      placeholder="搜索操作详情..." 
                      class="filter-input"
                    />
                  </div>
                  <div class="filter-item">
                    <button @click="filterLogs" class="filter-btn">
                      <i class="el-icon-search"></i>
                      搜索
                    </button>
                  </div>
                </div>
              </div>
              
              <!-- 日志表格 -->
              <div class="logs-table">
                <div class="table-header">
                  <div class="table-cell">时间</div>
                  <div class="table-cell">用户</div>
                  <div class="table-cell">操作类型</div>
                  <div class="table-cell">模块</div>
                  <div class="table-cell">操作详情</div>
                  <div class="table-cell">结果</div>
                  <div class="table-cell">IP地址</div>
                </div>
                <div class="table-body">
                  <div v-if="logs.length === 0" class="no-logs">
                    <i class="el-icon-document"></i>
                    <p>暂无操作日志</p>
                  </div>
                  <div v-else>
                    <div v-for="log in logs" :key="log.id" class="table-row">
                      <div class="table-cell">{{ formatTime(log.created_at) }}</div>
                      <div class="table-cell">{{ log.username }}</div>
                      <div class="table-cell">
                        <span :class="getOperationTypeClass(log.operation_type)">
                          {{ formatOperationType(log.operation_type) }}
                        </span>
                      </div>
                      <div class="table-cell">{{ log.module }}</div>
                      <div class="table-cell">{{ log.operation_detail }}</div>
                      <div class="table-cell">
                        <span :class="log.success ? 'success' : 'failed'">
                          {{ log.success ? '成功' : '失败' }}
                        </span>
                      </div>
                      <div class="table-cell">{{ log.ip_address }}</div>
                    </div>
                  </div>
                </div>
              </div>
              
              <!-- 分页 -->
              <div class="logs-pagination">
                <button 
                  @click="prevPage" 
                  :disabled="logPagination.page <= 1"
                  class="page-btn"
                >
                  上一页
                </button>
                <span class="page-info">
                  第 {{ logPagination.page }} 页，共 {{ logPagination.total_pages }} 页
                </span>
                <button 
                  @click="nextPage" 
                  :disabled="logPagination.page >= logPagination.total_pages"
                  class="page-btn"
                >
                  下一页
                </button>
              </div>
            </div>

            <!-- 其他页面占位符 -->
            <div v-else class="placeholder-content">
              <div class="placeholder-icon">
                <i class="el-icon-info"></i>
              </div>
              <h3>功能开发中</h3>
              <p>{{ getMenuTitle(activeMenu) }} 功能正在开发中，敬请期待！</p>
            </div>
          </div>
        </div>
      </div>
        </div>
      </div>
    </div>

    <!-- 用户管理对话框 -->
    <el-dialog
      v-model="userDialogVisible"
      :title="isEditMode ? '编辑用户' : '添加用户'"
      width="500px"
    >
      <el-form
        :model="userForm"
        :rules="userFormRules"
        ref="userFormRef"
        label-width="80px"
      >
        <el-form-item label="用户名" prop="username">
          <el-input
            v-model="userForm.username"
            placeholder="请输入用户名"
            :disabled="isEditMode || userForm.username === 'admin'"
          />
        </el-form-item>
        <el-form-item label="密码" prop="password">
          <el-input
            v-model="userForm.password"
            type="password"
            placeholder="请输入密码"
            show-password
          />
        </el-form-item>
        <el-form-item label="状态" prop="is_active">
          <el-switch
            v-model="userForm.is_active"
            active-text="启用"
            inactive-text="禁用"
            :disabled="userForm.username === 'admin'"
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="userDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="saveUser" :loading="saveLoading">
            {{ isEditMode ? '更新' : '创建' }}
          </el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 黑名单管理对话框 -->
    <el-dialog
      v-model="blacklistDialogVisible"
      title="添加黑名单"
      width="500px"
    >
      <el-form
        :model="blacklistForm"
        label-width="100px"
      >
        <el-form-item label="IP地址" required>
          <el-input
            v-model="blacklistForm.ip"
            placeholder="请输入IP地址"
          />
        </el-form-item>
        <el-form-item label="用户名">
          <el-input
            v-model="blacklistForm.username"
            placeholder="请输入用户名（可选）"
          />
        </el-form-item>
        <el-form-item label="封禁原因" required>
          <el-input
            v-model="blacklistForm.reason"
            type="textarea"
            placeholder="请输入封禁原因"
            rows="3"
          />
        </el-form-item>
        <el-form-item label="封禁时长" required>
          <el-input-number
            v-model="blacklistForm.hours"
            :min="1"
            :max="168"
            placeholder="小时"
            style="width: 100%"
          />
          <div style="font-size: 12px; color: #666; margin-top: 5px;">
            封禁时长（1-168小时，即最多7天）
          </div>
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="blacklistDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="addToBlacklist">
            添加黑名单
          </el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 验证码弹出窗口 -->
    <div v-if="showCaptchaModal" class="captcha-overlay">
      <div class="captcha-modal">
        <div class="captcha-header">
          <h3>安全验证</h3>
          <div class="captcha-subtitle">请按数字顺序点击红点（共{{ count }}个）</div>
        </div>
        <div class="captcha-content">
          <div v-if="loading" class="captcha-loading">验证码加载中...</div>
          <div v-else class="captcha-image-container">
            <img :src="img" @click="onImgClick" ref="imgRef" class="captcha-image"/>
            <template v-for="(p, i) in clicks" :key="i">
              <span :style="pointStyle(p)" class="click-marker">x</span>
            </template>
          </div>
          <div class="captcha-actions">
            <el-button type="primary" @click="submit" :disabled="clicks.length!==count">
              提交验证 ({{ clicks.length }}/{{ count }})
            </el-button>
            <el-button @click="refreshCaptcha">刷新</el-button>
            <el-button @click="cancelLogin">取消</el-button>
          </div>
          <div v-if="msg" class="captcha-message" :style="{color:msgColor}">{{ msg }}</div>
        </div>
      </div>
    </div>
          </div>
        </div>
      </div>
    </div>
          </div>
        </div>
      </div>
    </div>
      </div>
    </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import axios from 'axios'

const img = ref('')
const id = ref('')
const count = ref(0)
const clicks = reactive([])
const loading = ref(false)
const msg = ref('')
const msgColor = ref('red')
const imgRef = ref(null)
const calibrationMode = ref(false)

// 登录相关状态
const isLoggedIn = ref(false)
const username = ref('')
const remainingUses = ref(0)
const loginLoading = ref(false)
const loginForm = reactive({
  username: '',
  password: ''
})

// 验证码弹窗状态
const showCaptchaModal = ref(false)
const tempLoginData = ref(null)

// 后台管理状态
const activeMenu = ref('dashboard')

// 用户管理状态
const users = ref([])
const filteredUsers = ref([])
const usersLoading = ref(false)
const searchKeyword = ref('')
const totalUsers = ref(0)

// 用户表单状态
const userDialogVisible = ref(false)
const isEditMode = ref(false)
const saveLoading = ref(false)
const userFormRef = ref(null)
const userForm = reactive({
  id: null,
  username: '',
  password: '',
  is_active: true
})

// MQTT服务器状态
const mqttStats = ref({
  port: 1883,
  running: false,
  clients: 0,
  topics: 0,
  client_list: [],
  topic_list: [],
  blacklist: [],
  failed_auth: [],
  config: {
    require_auth: true,
    allow_anonymous: false,
    max_failed_attempts: 3,
    blacklist_duration: 24,
    cleanup_interval: 30
  }
})

// MQTT消息发布表单
const publishForm = reactive({
  topic: '',
  message: ''
})
const publishLoading = ref(false)

// MQTT单发消息表单
const sendForm = reactive({
  clientId: '',
  topic: '',
  message: ''
})
const sendLoading = ref(false)
const activeTab = ref('broadcast')

// MQTT配置表单
const mqttConfig = reactive({
  require_auth: true,
  allow_anonymous: false,
  max_failed_attempts: 3,
  blacklist_duration: 24,
  cleanup_interval: 30
})
const configLoading = ref(false)

// MQTT管理界面状态
const mqttActiveTab = ref('clients')

// 黑名单管理
const blacklistDialogVisible = ref(false)
const blacklistForm = reactive({
  ip: '',
  username: '',
  reason: '',
  hours: 24
})

// 操作日志相关数据变量
const logs = ref([])
const logStats = ref({
  today_count: 0,
  success_rate: 0,
  operation_types: {}
})
const logFilters = reactive({
  operation_type: '',
  module: '',
  keyword: ''
})
const logPagination = reactive({
  page: 1,
  total_pages: 1,
  total_count: 0,
  page_size: 20
})
const logsLoading = ref(false)

// 用户表单验证规则
const userFormRules = {
  username: [
    { required: true, message: '请输入用户名', trigger: 'blur' },
    { min: 3, max: 20, message: '用户名长度应在3到20个字符之间', trigger: 'blur' }
  ],
  password: [
    { required: true, message: '请输入密码', trigger: 'blur' },
    { min: 6, message: '密码长度至少6个字符', trigger: 'blur' }
  ]
}

// 从localStorage获取token
const getToken = () => localStorage.getItem('auth_token')
const setToken = (token) => localStorage.setItem('auth_token', token)
const removeToken = () => localStorage.removeItem('auth_token')

// 设置axios默认headers
const setAuthHeader = (token) => {
  if (token) {
    axios.defaults.headers.common['Authorization'] = `Bearer ${token}`
  } else {
    delete axios.defaults.headers.common['Authorization']
  }
}

function pointStyle(p) {
  return {
    position: 'absolute',
    left: p.x - 6 + 'px',
    top: p.y - 6 + 'px',
    width: '12px',
    height: '12px',
    background: 'blue',
    borderRadius: '50%',
    border: '2px solid #fff',
    pointerEvents: 'none',
    boxShadow: '0 0 2px #0008'
  }
}

// 检查登录状态
async function checkAuthStatus() {
  const token = getToken()
  if (!token) {
    isLoggedIn.value = false
    return
  }
  
  setAuthHeader(token)
  try {
    const res = await axios.get('/api/auth/status')
    isLoggedIn.value = true
    remainingUses.value = res.data.remaining_uses
    
    // 从token中解析用户名（简单实现）
    const payload = JSON.parse(atob(token.split('.')[1]))
    username.value = payload.username
  } catch (e) {
    // token无效，清除登录状态
    logout()
  }
}


// 处理登录
async function handleLogin() {
  if (!loginForm.username || !loginForm.password) {
    ElMessage.error('请输入用户名和密码')
    return
  }
  
  loginLoading.value = true
  
  // 直接加载验证码，不先验证账号密码
  try {
    const res = await axios.post('/api/captcha/prepare', {
      username: loginForm.username,
      password: loginForm.password
    })
    
    // 显示验证码
    img.value = res.data.img
    id.value = res.data.id
    count.value = res.data.count
    clicks.splice(0)
    msg.value = ''
    
    // 临时存储登录信息，等验证码通过后再验证
    tempLoginData.value = {
      username: loginForm.username,
      password: loginForm.password
    }
    showCaptchaModal.value = true
    
  } catch (e) {
    const errorMsg = e.response?.data?.error || '获取验证码失败'
    ElMessage.error(errorMsg)
  }
  loginLoading.value = false
}

// 取消登录
function cancelLogin() {
  tempLoginData.value = null
  showCaptchaModal.value = false
  img.value = ''
  clicks.splice(0)
  msg.value = ''
  loginLoading.value = false
}

// 退出登录
function logout() {
  removeToken()
  setAuthHeader(null)
  isLoggedIn.value = false
  username.value = ''
  remainingUses.value = 0
  img.value = ''
  tempLoginData.value = null
  showCaptchaModal.value = false
  ElMessage.info('已退出登录')
}

async function loadCaptcha() {
  if (!isLoggedIn.value) return
  
  loading.value = true
  msg.value = ''
  clicks.splice(0)
  try {
    const res = await axios.get('/api/captcha')
    img.value = res.data.img
    id.value = res.data.id
    count.value = res.data.count
    remainingUses.value = res.data.remaining_uses
  } catch (e) {
    if (e.response?.status === 401) {
      logout()
      ElMessage.error('登录已过期，请重新登录')
    } else {
      ElMessage.error('获取验证码失败')
    }
  }
  loading.value = false
}

function onImgClick(e) {
  const rect = imgRef.value.getBoundingClientRect()
  
  // 坐标转换
  const scaleX = imgRef.value.naturalWidth / rect.width
  const scaleY = imgRef.value.naturalHeight / rect.height
  const x = Math.round((e.clientX - rect.left) * scaleX)
  const y = Math.round((e.clientY - rect.top) * scaleY)
  
  if (calibrationMode.value) {
    // 校准模式：发送坐标到服务器
    calibrateCoordinate(x, y)
    return
  }
  
  if (clicks.length >= count.value) return
  clicks.push({ x, y })
}

async function submit() {
  try {
    // 如果有临时登录数据，说明是登录流程
    if (tempLoginData.value) {
      const res = await axios.post('/api/captcha/verify-login', {
        id: id.value,
        clicks: clicks,
        username: tempLoginData.value.username,
        password: tempLoginData.value.password
      })
      
      if (res.data.success) {
        // 登录成功
        const token = res.data.token
        setToken(token)
        setAuthHeader(token)
        
        isLoggedIn.value = true
        username.value = res.data.user.username
        remainingUses.value = res.data.auth.remaining_uses
        
        // 清除临时数据
        tempLoginData.value = null
        showCaptchaModal.value = false
        
        // 显示成功消息
        ElMessage.success('登录成功')
        
        // 登录成功后自动加载验证码
        loadCaptcha()
      }
    } else {
      // 普通验证码验证
      const res = await axios.post('/api/captcha/verify', {
        id: id.value,
        clicks: clicks
      })
      msg.value = res.data.msg
      msgColor.value = 'green'
      
      // 更新剩余使用次数
      if (res.data.remaining_uses !== undefined) {
        remainingUses.value = res.data.remaining_uses
      }
    }
  } catch (e) {
    if (e.response?.status === 401) {
      const errorMsg = e.response?.data?.error || '认证失败'
      
      // 如果是登录流程，显示错误并关闭弹窗
      if (tempLoginData.value) {
        ElMessage.error(errorMsg)
        tempLoginData.value = null
        showCaptchaModal.value = false
      } else {
        logout()
        ElMessage.error('登录已过期，请重新登录')
      }
    } else {
      const errorMsg = e.response?.data?.msg || e.response?.data?.error || '验证失败'
      
      // 验证码错误时显示在弹窗内
      if (tempLoginData.value) {
        msg.value = errorMsg
        msgColor.value = 'red'
      } else {
        ElMessage.error(errorMsg)
      }
    }
  }
}

function refresh() {
  loadCaptcha()
}

// 在登录流程中刷新验证码
async function refreshCaptcha() {
  if (!tempLoginData.value) return
  
  loading.value = true
  msg.value = ''
  clicks.splice(0)
  
  try {
    const res = await axios.post('/api/captcha/prepare', {
      username: tempLoginData.value.username,
      password: tempLoginData.value.password
    })
    
    // 更新验证码
    img.value = res.data.img
    id.value = res.data.id
    count.value = res.data.count
    
  } catch (e) {
    const errorMsg = e.response?.data?.error || '获取验证码失败'
    ElMessage.error(errorMsg)
  }
  loading.value = false
}

function calibrate() {
  calibrationMode.value = !calibrationMode.value
  if (calibrationMode.value) {
    msg.value = '校准模式：请点击红色圆圈的中心位置'
    msgColor.value = 'blue'
  } else {
    msg.value = '退出校准模式'
    msgColor.value = 'green'
  }
}

async function calibrateCoordinate(x, y) {
  try {
    const res = await axios.post('/api/calibrate', {
      click: { x, y }
    })
    msg.value = `校准点击: 浏览器坐标(${x},${y}) - ${res.data.msg}`
    msgColor.value = 'green'
  } catch (e) {
    ElMessage.error('校准失败')
  }
}

// 菜单处理函数
function handleMenuSelect(key) {
  activeMenu.value = key
  if (key === 'captcha') {
    // 切换到验证码页面时自动加载验证码
    if (!img.value) {
      loadCaptcha()
    }
  } else if (key === 'users') {
    // 切换到用户管理页面时加载用户列表
    loadUsers()
  } else if (key === 'mqtt') {
    // 切换到MQTT管理页面时加载MQTT状态
    loadMqttStats()
  } else if (key === 'logs') {
    // 切换到操作日志页面时加载日志
    loadLogs()
    loadLogStats()
  }
}

function getMenuTitle(key) {
  const titles = {
    dashboard: '仪表盘',
    captcha: '验证码测试',
    users: '用户管理',
    mqtt: 'MQTT服务器',
    logs: '操作日志',
    settings: '系统设置'
  }
  return titles[key] || '未知页面'
}

// 用户管理函数
async function loadUsers() {
  usersLoading.value = true
  try {
    const res = await axios.get('/api/users')
    users.value = res.data.users || []
    filteredUsers.value = [...users.value]
    totalUsers.value = users.value.length
  } catch (e) {
    ElMessage.error('加载用户列表失败')
  }
  usersLoading.value = false
}

function handleSearch() {
  if (!searchKeyword.value) {
    filteredUsers.value = [...users.value]
  } else {
    filteredUsers.value = users.value.filter(user => 
      user.username.toLowerCase().includes(searchKeyword.value.toLowerCase())
    )
  }
}

function showAddUserDialog() {
  isEditMode.value = false
  userForm.id = null
  userForm.username = ''
  userForm.password = ''
  userForm.is_active = true
  userDialogVisible.value = true
}

function editUser(user) {
  isEditMode.value = true
  userForm.id = user.id
  userForm.username = user.username
  userForm.password = ''
  userForm.is_active = user.is_active
  userDialogVisible.value = true
}

async function saveUser() {
  if (!userFormRef.value) return
  
  await userFormRef.value.validate(async (valid) => {
    if (valid) {
      saveLoading.value = true
      try {
        if (isEditMode.value) {
          await axios.put(`/api/users/${userForm.id}`, userForm)
          ElMessage.success('用户更新成功')
        } else {
          await axios.post('/api/users', userForm)
          ElMessage.success('用户创建成功')
        }
        userDialogVisible.value = false
        loadUsers()
      } catch (e) {
        ElMessage.error(e.response?.data?.error || '操作失败')
      }
      saveLoading.value = false
    }
  })
}

async function deleteUser(user) {
  if (user.username === username.value) {
    ElMessage.warning('不能删除当前登录用户')
    return
  }
  
  if (user.username === 'admin') {
    ElMessage.warning('admin为超级管理员账号，不能删除')
    return
  }
  
  try {
    await ElMessageBox.confirm(`确定要删除用户 "${user.username}" 吗？`, '确认删除', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })
    
    await axios.delete(`/api/users/${user.id}`)
    ElMessage.success('用户删除成功')
    loadUsers()
  } catch (e) {
    if (e !== 'cancel') {
      ElMessage.error(e.response?.data?.error || '删除失败')
    }
  }
}

function formatDate(dateString) {
  if (!dateString) return ''
  const date = new Date(dateString)
  return date.toLocaleString('zh-CN')
}

// MQTT管理函数
async function loadMqttStats() {
  try {
    const res = await axios.get('/api/mqtt/stats')
    mqttStats.value = res.data.stats
    
    // 更新配置表单
    if (mqttStats.value.config) {
      Object.assign(mqttConfig, mqttStats.value.config)
    }
  } catch (e) {
    ElMessage.error('加载MQTT状态失败')
    // 设置默认值
    mqttStats.value = {
      port: 1883,
      running: false,
      clients: 0,
      topics: 0,
      client_list: [],
      topic_list: [],
      blacklist: [],
      failed_auth: [],
      config: {
        require_auth: true,
        allow_anonymous: false,
        max_failed_attempts: 3,
        blacklist_duration: 24,
        cleanup_interval: 30
      }
    }
  }
}

async function publishMessage() {
  if (!publishForm.topic || !publishForm.message) {
    ElMessage.error('请输入主题和消息内容')
    return
  }
  
  publishLoading.value = true
  try {
    await axios.post('/api/mqtt/publish', {
      topic: publishForm.topic,
      message: publishForm.message
    })
    ElMessage.success('消息发布成功')
    publishForm.topic = ''
    publishForm.message = ''
    // 刷新统计信息
    loadMqttStats()
  } catch (e) {
    ElMessage.error(e.response?.data?.error || '消息发布失败')
  }
  publishLoading.value = false
}

async function sendToClient() {
  if (!sendForm.clientId || !sendForm.topic || !sendForm.message) {
    ElMessage.error('请选择客户端并输入主题和消息内容')
    return
  }
  
  // 确保客户端ID是字符串
  const clientId = typeof sendForm.clientId === 'object' ? sendForm.clientId.id : sendForm.clientId
  
  // 调试信息
  console.log('=== MQTT发送调试信息 ===')
  console.log('原始 sendForm.clientId:', sendForm.clientId)
  console.log('原始 sendForm.clientId 类型:', typeof sendForm.clientId)
  console.log('处理后的 clientId:', clientId)
  console.log('处理后的 clientId 类型:', typeof clientId)
  
  const payload = {
    client_id: String(clientId),
    topic: String(sendForm.topic),
    message: String(sendForm.message)
  }
  console.log('最终发送的数据:', payload)
  
  sendLoading.value = true
  try {
    await axios.post('/api/mqtt/send', payload)
    ElMessage.success(`消息已发送到客户端 ${sendForm.clientId}`)
    sendForm.clientId = ''
    sendForm.topic = ''
    sendForm.message = ''
    // 刷新统计信息
    loadMqttStats()
  } catch (e) {
    ElMessage.error(e.response?.data?.error || '消息发送失败')
  }
  sendLoading.value = false
}

// 黑名单管理函数
function showAddBlacklistDialog() {
  blacklistForm.ip = ''
  blacklistForm.username = ''
  blacklistForm.reason = ''
  blacklistForm.hours = 24
  blacklistDialogVisible.value = true
}

async function addToBlacklist() {
  if (!blacklistForm.ip || !blacklistForm.reason || !blacklistForm.hours) {
    ElMessage.error('请填写完整的黑名单信息')
    return
  }
  
  try {
    await axios.post('/api/mqtt/blacklist', {
      ip: blacklistForm.ip,
      username: blacklistForm.username,
      reason: blacklistForm.reason,
      hours: blacklistForm.hours
    })
    ElMessage.success('黑名单添加成功')
    blacklistDialogVisible.value = false
    loadMqttStats() // 刷新状态
  } catch (e) {
    ElMessage.error(e.response?.data?.error || '添加黑名单失败')
  }
}

async function removeFromBlacklist(ip) {
  try {
    await ElMessageBox.confirm(`确定要移除黑名单 ${ip} 吗？`, '确认移除', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })
    
    await axios.delete(`/api/mqtt/blacklist/${ip}`)
    ElMessage.success('黑名单移除成功')
    loadMqttStats() // 刷新状态
  } catch (e) {
    if (e !== 'cancel') {
      ElMessage.error(e.response?.data?.error || '移除黑名单失败')
    }
  }
}

async function clearFailedAuth(ip) {
  try {
    await ElMessageBox.confirm(`确定要清除 ${ip} 的失败认证记录吗？`, '确认清除', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })
    
    await axios.delete(`/api/mqtt/failedauth/${ip}`)
    ElMessage.success('失败认证记录清除成功')
    loadMqttStats() // 刷新状态
  } catch (e) {
    if (e !== 'cancel') {
      ElMessage.error(e.response?.data?.error || '清除失败认证记录失败')
    }
  }
}

// 配置管理函数
async function updateMqttConfig() {
  configLoading.value = true
  try {
    await axios.put('/api/mqtt/config', mqttConfig)
    ElMessage.success('配置更新成功')
    loadMqttStats() // 刷新状态
  } catch (e) {
    ElMessage.error(e.response?.data?.error || '配置更新失败')
  }
  configLoading.value = false
}

// 操作日志管理函数
async function loadLogs() {
  logsLoading.value = true
  try {
    const params = new URLSearchParams({
      page: logPagination.page,
      page_size: logPagination.page_size,
      ...logFilters
    })
    
    const response = await axios.get(`/api/logs?${params}`)
    logs.value = response.data.logs || []
    logPagination.total_count = response.data.total || 0
    logPagination.total_pages = response.data.total_pages || 1
  } catch (e) {
    ElMessage.error(e.response?.data?.error || '加载日志失败')
    logs.value = []
  }
  logsLoading.value = false
}

async function loadLogStats() {
  try {
    const response = await axios.get('/api/logs/stats')
    logStats.value = response.data.stats || {}
  } catch (e) {
    console.error('Failed to load log stats:', e)
  }
}

async function refreshLogs() {
  await loadLogs()
  await loadLogStats()
  ElMessage.success('日志已刷新')
}

async function clearLogs() {
  try {
    await ElMessageBox.confirm('确定要清理30天前的操作日志吗？', '确认清理', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })
    
    const response = await axios.delete('/api/logs/clear?days=30')
    ElMessage.success(`成功清理了 ${response.data.rows_affected} 条日志`)
    await loadLogs()
    await loadLogStats()
  } catch (e) {
    if (e !== 'cancel') {
      ElMessage.error(e.response?.data?.error || '清理日志失败')
    }
  }
}

async function filterLogs() {
  logPagination.page = 1
  await loadLogs()
}

function formatTime(timeStr) {
  if (!timeStr) return ''
  const date = new Date(timeStr)
  const now = new Date()
  const today = new Date(now.getFullYear(), now.getMonth(), now.getDate())
  const logDate = new Date(date.getFullYear(), date.getMonth(), date.getDate())
  
  // 如果是今天，只显示时间
  if (logDate.getTime() === today.getTime()) {
    return date.toLocaleString('zh-CN', {
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit'
    })
  }
  
  // 如果是昨天，显示"昨天 HH:mm"
  const yesterday = new Date(today.getTime() - 24 * 60 * 60 * 1000)
  if (logDate.getTime() === yesterday.getTime()) {
    return '昨天 ' + date.toLocaleString('zh-CN', {
      hour: '2-digit',
      minute: '2-digit'
    })
  }
  
  // 其他情况显示完整日期时间，但使用更紧凑的格式
  return date.toLocaleString('zh-CN', {
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit'
  })
}

function formatOperationType(type) {
  const typeMap = {
    'LOGIN': '登录',
    'LOGIN_FAILED': '登录失败',
    'CREATE_USER': '创建用户',
    'CREATE_USER_FAILED': '创建用户失败',
    'UPDATE_USER': '更新用户',
    'UPDATE_USER_FAILED': '更新用户失败',
    'DELETE_USER': '删除用户',
    'DELETE_USER_FAILED': '删除用户失败',
    'CLEAR_LOGS': '清理日志',
    'CLEAR_LOGS_FAILED': '清理日志失败'
  }
  return typeMap[type] || type
}

function getOperationTypeClass(type) {
  if (type.includes('FAILED')) {
    return 'operation-failed'
  }
  if (type.includes('LOGIN')) {
    return 'operation-login'
  }
  if (type.includes('CREATE') || type.includes('UPDATE') || type.includes('DELETE')) {
    return 'operation-manage'
  }
  return 'operation-other'
}

function prevPage() {
  if (logPagination.page > 1) {
    logPagination.page--
    loadLogs()
  }
}

function nextPage() {
  if (logPagination.page < logPagination.total_pages) {
    logPagination.page++
    loadLogs()
  }
}

onMounted(() => {
  checkAuthStatus()
})
</script>

<style scoped>
.app-container {
  min-height: 100vh;
  height: 100%;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 0;
}

/* 所有分辨率都移除padding，使用内部组件控制间距 */

.page-header {
  text-align: center;
  margin-bottom: 40px;
  color: white;
  padding: 40px 20px 0;
}

.page-title {
  font-size: 2.5em;
  margin-bottom: 10px;
  font-weight: 300;
  text-shadow: 0 2px 4px rgba(0,0,0,0.3);
}

.page-title i {
  margin-right: 12px;
  font-size: 1.2em;
}

.page-subtitle {
  font-size: 1.2em;
  opacity: 0.9;
  margin: 0;
}

/* 高分辨率标题适配 */
@media (min-width: 1920px) {
  .page-title {
    font-size: 3.5em;
  }
  
  .page-subtitle {
    font-size: 1.5em;
  }
}

/* 中等分辨率标题适配 */
@media (min-width: 1200px) and (max-width: 1919px) {
  .page-title {
    font-size: 3em;
  }
  
  .page-subtitle {
    font-size: 1.3em;
  }
}

.login-section {
  display: flex;
  justify-content: center;
  align-items: flex-start;
  margin-bottom: 40px;
  padding: 0 20px;
}

.login-card {
  background: white;
  border-radius: 12px;
  padding: 40px;
  box-shadow: 0 20px 40px rgba(0,0,0,0.1);
  max-width: 400px;
  width: 100%;
  backdrop-filter: blur(10px);
}

/* 高分辨率登录卡片适配 */
@media (min-width: 1920px) {
  .login-card {
    max-width: 500px;
    padding: 50px;
  }
}

/* 中等分辨率登录卡片适配 */
@media (min-width: 1200px) and (max-width: 1919px) {
  .login-card {
    max-width: 450px;
    padding: 45px;
  }
}

/* 移动端登录卡片适配 */
@media (max-width: 768px) {
  .login-card {
    padding: 30px 20px;
    max-width: 100%;
    margin: 0 10px;
  }
}

.login-header {
  text-align: center;
  margin-bottom: 30px;
}

.login-header h2 {
  color: #333;
  font-size: 1.8em;
  margin-bottom: 8px;
  font-weight: 500;
}

.login-header p {
  color: #666;
  margin: 0;
}

.login-form {
  margin-bottom: 20px;
}

.login-form .el-form-item {
  margin-bottom: 20px;
}

.login-button {
  width: 100%;
  height: 50px;
  font-size: 16px;
  border-radius: 8px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border: none;
  transition: all 0.3s ease;
}

.login-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
}


.user-info-card {
  background: white;
  border-radius: 12px;
  padding: 30px;
  box-shadow: 0 20px 40px rgba(0,0,0,0.1);
  max-width: 400px;
  width: 100%;
  display: flex;
  align-items: center;
  gap: 20px;
}

.user-avatar {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 24px;
}

.user-details {
  flex: 1;
}

.user-details h3 {
  margin: 0 0 5px 0;
  color: #333;
  font-size: 1.2em;
}

.user-details p {
  margin: 0;
  color: #666;
}

.remaining-count {
  color: #667eea;
  font-weight: 600;
}

.logout-btn {
  border-radius: 6px;
}

/* 后台管理页面样式 */
.admin-layout {
  display: flex;
  height: 100vh;
  background: #f5f5f5;
}

/* 移动端后台管理适配 */
@media (max-width: 768px) {
  .admin-layout {
    flex-direction: column;
    height: auto;
    min-height: 100vh;
  }
}

.sidebar {
  width: 250px;
  background: #2c3e50;
  color: white;
  flex-shrink: 0;
}

/* 高分辨率侧边栏适配 */
@media (min-width: 1920px) {
  .sidebar {
    width: 300px;
  }
}

/* 中等分辨率侧边栏适配 */
@media (min-width: 1200px) and (max-width: 1919px) {
  .sidebar {
    width: 280px;
  }
}

/* 小屏幕PC侧边栏适配 */
@media (min-width: 768px) and (max-width: 1199px) {
  .sidebar {
    width: 220px;
  }
}

/* 移动端侧边栏适配 */
@media (max-width: 768px) {
  .sidebar {
    width: 100%;
    height: auto;
  }
}

.sidebar-header {
  padding: 20px;
  border-bottom: 1px solid #34495e;
  text-align: center;
}

.sidebar-header h3 {
  margin: 0 0 5px 0;
  font-size: 1.2em;
}

.sidebar-header .version {
  font-size: 0.8em;
  color: #95a5a6;
}

.sidebar-menu {
  border: none;
}

.sidebar-menu .el-menu-item {
  height: 50px;
  line-height: 50px;
  border-bottom: 1px solid #34495e;
}

/* 移动端菜单适配 */
@media (max-width: 768px) {
  .sidebar-menu {
    display: flex;
    overflow-x: auto;
    white-space: nowrap;
  }
  
  .sidebar-menu .el-menu-item {
    flex: 0 0 auto;
    min-width: 120px;
    text-align: center;
    border-bottom: none;
    border-right: 1px solid #34495e;
  }
  
  .sidebar-menu .el-menu-item:last-child {
    border-right: none;
  }
}

.sidebar-menu .el-menu-item:hover {
  background-color: #34495e;
}

.sidebar-menu .el-menu-item.is-active {
  background-color: #3498db;
  color: #ffffff !important;
  font-weight: 600;
}

.sidebar-menu .el-menu-item.is-active i {
  color: #ffffff !important;
}

.sidebar-menu .el-menu-item.is-active span {
  color: #ffffff !important;
}

.sidebar-menu .el-menu-item {
  display: flex;
  align-items: center;
  justify-content: flex-start;
}

.sidebar-menu .el-menu-item i {
  margin-right: 10px;
  font-size: 16px;
  flex-shrink: 0;
}

.sidebar-menu .el-menu-item span {
  flex: 1;
  text-align: left;
}

.main-content {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.header {
  height: 60px;
  background: white;
  border-bottom: 1px solid #e0e0e0;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 20px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.header-left h2 {
  margin: 0;
  color: #333;
  font-size: 1.4em;
}

.header-right .user-info {
  display: flex;
  align-items: center;
  gap: 15px;
}

/* 移动端顶部导航适配 */
@media (max-width: 768px) {
  .header {
    padding: 0 10px;
    flex-direction: column;
    height: auto;
    padding: 15px 10px;
  }
  
  .header-left {
    margin-bottom: 10px;
  }
  
  .header-right .user-info {
    gap: 10px;
    font-size: 14px;
  }
  
  .header-right .user-info .welcome-text,
  .header-right .user-info .remaining-uses {
    display: none;
  }
}

.welcome-text {
  color: #333;
  font-weight: 500;
}

.remaining-uses {
  color: #666;
  font-size: 0.9em;
}

.content-area {
  flex: 1;
  padding: 20px;
  overflow-y: auto;
}

/* 高分辨率内容区域适配 */
@media (min-width: 1920px) {
  .content-area {
    padding: 40px;
  }
}

/* 中等分辨率内容区域适配 */
@media (min-width: 1200px) and (max-width: 1919px) {
  .content-area {
    padding: 30px;
  }
}

/* 小屏幕PC内容区域适配 */
@media (min-width: 768px) and (max-width: 1199px) {
  .content-area {
    padding: 20px;
  }
}

/* 移动端内容区域适配 */
@media (max-width: 768px) {
  .content-area {
    padding: 10px;
  }
}

/* 仪表盘样式 */
.dashboard {
  max-width: 1200px;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(6, 1fr) !important;
  gap: 8px;
  margin-bottom: 20px;
}

/* 高分辨率仪表盘适配 */
@media (min-width: 1920px) {
  .dashboard {
    max-width: 1600px;
  }
  
  .stats-grid {
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 30px;
    margin-bottom: 40px;
  }
}

/* 中等分辨率仪表盘适配 */
@media (min-width: 1200px) and (max-width: 1919px) {
  .dashboard {
    max-width: 1400px;
  }
  
  .stats-grid {
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 25px;
    margin-bottom: 35px;
  }
}

/* 小屏幕PC仪表盘适配 */
@media (min-width: 768px) and (max-width: 1199px) {
  .dashboard {
    max-width: 100%;
  }
  
  .stats-grid {
    grid-template-columns: repeat(6, 1fr);
    gap: 8px;
  }
}

/* 移动端统计卡片适配 */
@media (max-width: 768px) {
  .stats-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 8px;
    margin-bottom: 15px;
  }
}

.stat-card {
  background: white;
  border-radius: 4px;
  padding: 8px 10px;
  box-shadow: 0 1px 3px rgba(0,0,0,0.1);
  display: flex;
  align-items: center;
  gap: 8px;
  transition: transform 0.2s ease;
}

.stat-card:hover {
  transform: translateY(-2px);
}

.stat-icon {
  width: 24px;
  height: 24px;
  border-radius: 50%;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 12px;
  flex-shrink: 0;
}

.stat-info h3 {
  margin: 0 0 5px 0;
  color: #333;
  font-size: 1em;
}

.stat-info h4 {
  margin: 0;
  color: #333 !important;
  font-size: 0.8em;
  line-height: 1.2;
}

.stat-info p {
  margin: 0;
  color: #666;
  font-size: 0.9em;
  font-weight: 600;
  line-height: 1.1;
}

/* 验证码测试样式 */
.captcha-test {
  max-width: 600px;
}

.captcha-container {
  background: white;
  border-radius: 8px;
  padding: 30px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

/* 高分辨率验证码测试适配 */
@media (min-width: 1920px) {
  .captcha-test {
    max-width: 800px;
  }
  
  .captcha-container {
    padding: 40px;
  }
}

/* 中等分辨率验证码测试适配 */
@media (min-width: 1200px) and (max-width: 1919px) {
  .captcha-test {
    max-width: 700px;
  }
  
  .captcha-container {
    padding: 35px;
  }
}

/* 小屏幕PC验证码测试适配 */
@media (min-width: 768px) and (max-width: 1199px) {
  .captcha-test {
    max-width: 100%;
  }
  
  .captcha-container {
    padding: 25px;
  }
}

.loading-area {
  text-align: center;
  padding: 40px;
  color: #666;
}

.loading-area i {
  font-size: 24px;
  margin-right: 10px;
}

.captcha-area {
  text-align: center;
}

.captcha-title {
  margin-bottom: 20px;
  color: #333;
  font-size: 1.1em;
}

.captcha-image-wrapper {
  position: relative;
  display: inline-block;
  margin-bottom: 20px;
  border: 2px solid #e0e0e0;
  border-radius: 8px;
  overflow: hidden;
}

.captcha-img {
  display: block;
  width: 300px;
  height: 150px;
  cursor: crosshair;
}

/* 移动端验证码图片适配 */
@media (max-width: 768px) {
  .captcha-img {
    width: 100%;
    max-width: 300px;
    height: auto;
  }
}

.click-point {
  position: absolute;
  width: 12px;
  height: 12px;
  background: #007bff;
  border-radius: 50%;
  border: 2px solid #fff;
  pointer-events: none;
  box-shadow: 0 0 4px rgba(0, 123, 255, 0.5);
  font-size: 0;
}

.captcha-actions {
  display: flex;
  justify-content: center;
  gap: 10px;
  margin-bottom: 20px;
}

/* 移动端验证码按钮适配 */
@media (max-width: 768px) {
  .captcha-actions {
    flex-direction: column;
    gap: 10px;
  }
  
  .captcha-actions .el-button {
    width: 100%;
  }
}

.captcha-message {
  padding: 10px;
  border-radius: 4px;
  background: #f5f5f5;
}

.no-captcha {
  text-align: center;
  padding: 40px;
}

/* 占位符内容样式 */
.placeholder-content {
  text-align: center;
  padding: 80px 20px;
  color: #666;
}

.placeholder-icon {
  font-size: 48px;
  margin-bottom: 20px;
  color: #ccc;
}

.placeholder-content h3 {
  margin: 0 0 10px 0;
  color: #333;
}

.placeholder-content p {
  margin: 0;
  color: #666;
}

/* 用户管理样式 */
.user-management {
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.user-toolbar {
  display: flex;
  justify-content: flex-start;
  align-items: center;
  margin-bottom: 20px;
  padding: 0 0 20px 0;
  border-bottom: 1px solid #eee;
  gap: 20px;
}

.search-box {
  display: flex;
  align-items: center;
}

.user-table-container {
  background: white;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

/* 移动端用户管理适配 */
@media (max-width: 768px) {
  .user-toolbar {
    flex-direction: column;
    gap: 15px;
    align-items: stretch;
  }
  
  .search-box {
    width: 100%;
  }
  
  .search-box .el-input {
    width: 100% !important;
  }
  
  .user-table-container {
    padding: 10px;
    overflow-x: auto;
  }
}

/* 用户表格操作按钮样式 */
.user-table-container .el-button {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  text-align: center;
  margin-right: 8px;
}

.user-table-container .el-button i {
  margin-right: 4px;
}

/* 用户工具栏按钮样式 */
.user-toolbar .el-button {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  text-align: center;
}

.user-toolbar .el-button i {
  margin-right: 4px;
}

/* MQTT管理样式 */
.mqtt-management {
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.mqtt-management h3 {
  margin-bottom: 20px;
  color: #333;
  font-size: 1.2em;
  border-bottom: 2px solid #e0e0e0;
  padding-bottom: 8px;
}

.mqtt-stats {
  margin-bottom: 30px;
}

.mqtt-clients, .mqtt-topics {
  margin-bottom: 30px;
}

.client-list, .topic-list {
  background: white;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.client-items, .topic-items {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
}

.client-item, .topic-item {
  background: #f5f5f5;
  padding: 8px 12px;
  border-radius: 4px;
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 14px;
}

.client-item i, .topic-item i {
  color: #666;
}

.no-clients, .no-topics {
  text-align: center;
  color: #999;
  padding: 20px;
}

.no-clients i, .no-topics i {
  font-size: 24px;
  margin-bottom: 8px;
  display: block;
}

.mqtt-publish {
  margin-bottom: 30px;
}

.publish-form {
  background: white;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
  position: relative;
  overflow: visible;
}

/* 修复下拉框滚动时位置偏移问题 */
.publish-form .el-select {
  position: relative;
}

.publish-form .el-input {
  position: relative;
}

/* 确保下拉框弹出层正确定位 */
.publish-form .el-select .el-select__popper {
  position: absolute !important;
  z-index: 9999 !important;
}

/* 全局修复Element Plus下拉框滚动问题 */
.el-popper {
  position: fixed !important;
  z-index: 9999 !important;
}

.el-select-dropdown {
  position: fixed !important;
  z-index: 9999 !important;
}

/* 防止滚动时下拉框闪烁 */
.el-select.is-focused .el-input__inner {
  border-color: #409eff !important;
}

/* 强制所有弹出层使用固定定位 */
.el-popper.is-pure {
  position: fixed !important;
}

/* 修复快速滚动时的定位更新 */
.el-select .el-select-dropdown {
  transform: translateZ(0) !important;
  will-change: transform !important;
}

/* 简单稳定的表单样式 */
.simple-form {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.input-group {
  display: flex;
  flex-direction: column;
  gap: 5px;
}

.input-group label {
  font-size: 14px;
  color: #606266;
  margin: 0;
}

.form-input {
  padding: 8px 12px;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  font-size: 14px;
  transition: border-color 0.2s;
  background: white;
  outline: none;
}

.form-input:focus {
  border-color: #409eff;
  box-shadow: 0 0 0 2px rgba(64, 158, 255, 0.2);
}

.form-input::placeholder {
  color: #c0c4cc;
}

.button-group {
  display: flex;
  justify-content: flex-start;
}

.publish-btn {
  padding: 8px 16px;
  background: #409eff;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
  transition: background-color 0.2s;
}

.publish-btn:hover:not(:disabled) {
  background: #337ecc;
}

.publish-btn:disabled {
  background: #a0cfff;
  cursor: not-allowed;
}

/* 下拉选择框样式 */
.form-select {
  padding: 8px 12px;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  font-size: 14px;
  background: white;
  outline: none;
  cursor: pointer;
  transition: border-color 0.2s;
}

.form-select:focus {
  border-color: #409eff;
  box-shadow: 0 0 0 2px rgba(64, 158, 255, 0.2);
}

/* 发送按钮样式 */
.send-btn {
  padding: 8px 16px;
  background: #67c23a;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
  transition: background-color 0.2s;
}

.send-btn:hover:not(:disabled) {
  background: #529b2e;
}

.send-btn:disabled {
  background: #a4da7a;
  cursor: not-allowed;
}

.text-success {
  color: #67c23a !important;
  font-weight: 600;
}

.text-danger {
  color: #f56c6c !important;
  font-weight: 600;
}

/* 移动端MQTT管理适配 */
@media (max-width: 768px) {
  .mqtt-management {
    max-width: 100%;
  }
  
  .client-items, .topic-items {
    flex-direction: column;
    gap: 8px;
  }
  
  .client-item, .topic-item {
    width: 100%;
    justify-content: flex-start;
  }
  
  .publish-form .el-form {
    flex-direction: column;
  }
  
  .publish-form .el-form-item {
    margin-bottom: 15px;
    width: 100%;
  }
  
  .publish-form .el-form-item .el-input,
  .publish-form .el-form-item .el-select {
    width: 100% !important;
  }
  
  .publish-form .el-tabs__content {
    padding: 10px 0;
  }
}

.captcha-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.6);
  backdrop-filter: blur(5px);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.captcha-modal {
  background: white;
  border-radius: 12px;
  padding: 24px;
  max-width: 400px;
  width: 90%;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
  animation: captchaSlideIn 0.3s ease-out;
}

/* 移动端验证码弹窗适配 */
@media (max-width: 768px) {
  .captcha-modal {
    width: 95%;
    max-width: none;
    margin: 0 10px;
    padding: 20px;
  }
  
  .captcha-modal .captcha-image {
    width: 100%;
    max-width: 300px;
    height: auto;
  }
  
  .captcha-modal .captcha-actions {
    flex-direction: column;
    gap: 10px;
  }
  
  .captcha-modal .captcha-actions .el-button {
    width: 100%;
  }
}

@keyframes captchaSlideIn {
  from {
    transform: translateY(-20px);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

.captcha-header {
  text-align: center;
  margin-bottom: 20px;
}

.captcha-header h3 {
  margin: 0 0 8px 0;
  color: #333;
  font-size: 18px;
}

.captcha-subtitle {
  color: #666;
  font-size: 14px;
}

.captcha-content {
  text-align: center;
}

.captcha-loading {
  padding: 40px;
  color: #666;
  font-size: 14px;
}

.captcha-image-container {
  position: relative;
  display: inline-block;
  margin-bottom: 20px;
  border: 2px solid #e0e0e0;
  border-radius: 8px;
  overflow: hidden;
}

.captcha-image {
  display: block;
  width: 300px;
  height: 150px;
  cursor: crosshair;
}

.click-marker {
  position: absolute;
  width: 12px;
  height: 12px;
  background: #007bff;
  border-radius: 50%;
  border: 2px solid #fff;
  pointer-events: none;
  box-shadow: 0 0 4px rgba(0, 123, 255, 0.5);
  font-size: 0;
}

.captcha-actions {
  display: flex;
  justify-content: center;
  gap: 12px;
  margin-bottom: 16px;
}

.captcha-message {
  font-size: 14px;
  margin-top: 8px;
  padding: 8px;
  border-radius: 4px;
  background: #f5f5f5;
}

/* MQTT管理界面样式 */
.mqtt-tabs {
  margin-bottom: 20px;
}

.mqtt-tabs .el-tabs__content {
  padding: 20px 0;
}

.security-section {
  margin-bottom: 30px;
}

.security-section h3 {
  margin-bottom: 15px;
  color: #333;
  font-size: 1.1em;
  border-bottom: 2px solid #e0e0e0;
  padding-bottom: 8px;
}

.blacklist-toolbar {
  margin-bottom: 15px;
  display: flex;
  justify-content: flex-start;
  align-items: center;
}

.blacklist-table, .failed-auth-table {
  background: white;
  border-radius: 8px;
  padding: 15px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
  margin-bottom: 20px;
}

.config-section {
  background: white;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.config-form {
  max-width: 600px;
}

.config-form .el-form-item {
  margin-bottom: 20px;
}

.config-form .el-form-item__label {
  font-weight: 500;
  color: #333;
}

.config-form .el-input-number {
  width: 100%;
}

/* 移动端MQTT管理适配 */
@media (max-width: 768px) {
  .mqtt-tabs .el-tabs__content {
    padding: 15px 0;
  }
  
  .blacklist-table, .failed-auth-table, .config-section {
    padding: 15px;
    margin-bottom: 15px;
  }
  
  .blacklist-toolbar {
    flex-direction: column;
    align-items: stretch;
    gap: 10px;
  }
  
  .config-form {
    max-width: 100%;
  }
}

/* 操作日志页面样式 */
.logs-management {
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.logs-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding-bottom: 15px;
  border-bottom: 2px solid #e0e0e0;
}

.logs-header h2 {
  margin: 0;
  color: #333 !important;
  font-size: 1.5em;
}

.logs-actions {
  display: flex;
  gap: 10px;
}

.refresh-btn, .clear-btn {
  padding: 8px 16px;
  border: 1px solid #ddd;
  border-radius: 4px;
  background: white;
  color: #333 !important;
  cursor: pointer;
  font-size: 14px;
  transition: all 0.2s;
}

.refresh-btn:hover {
  background: #f0f9ff;
  border-color: #409eff;
  color: #409eff !important;
}

.clear-btn:hover {
  background: #fef0f0;
  border-color: #f56c6c;
  color: #f56c6c !important;
}

.logs-stats {
  display: flex;
  gap: 30px;
  margin-bottom: 20px;
  padding: 15px;
  background: #f8f9fa;
  border-radius: 8px;
}

.stat-item {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.stat-label {
  font-size: 14px;
  color: #666 !important;
  margin-bottom: 5px;
}

.stat-value {
  font-size: 24px;
  font-weight: bold;
  color: #333 !important;
}

.logs-filters {
  margin-bottom: 20px;
  padding: 15px;
  background: white;
  border-radius: 8px;
  border: 1px solid #e0e0e0;
}

.filter-row {
  display: flex;
  gap: 20px;
  align-items: center;
  flex-wrap: wrap;
}

.filter-item {
  display: flex;
  align-items: center;
  gap: 8px;
}

.filter-item label {
  font-size: 14px;
  color: #666 !important;
  white-space: nowrap;
}

.filter-select, .filter-input {
  padding: 6px 12px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 14px;
  color: #333 !important;
  background: white;
}

.filter-select {
  min-width: 120px;
}

.filter-input {
  min-width: 200px;
}

.filter-btn {
  padding: 6px 16px;
  background: #409eff;
  color: white !important;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
  transition: background-color 0.2s;
}

.filter-btn:hover {
  background: #337ecc;
  color: white !important;
}

.logs-table {
  background: white;
  border-radius: 8px;
  border: 1px solid #e0e0e0;
  overflow-x: auto;
  margin-bottom: 20px;
}

.table-header {
  display: flex;
  background: #f8f9fa;
  border-bottom: 1px solid #e0e0e0;
  font-weight: bold;
  color: #333 !important;
}

.table-body {
  min-height: 200px;
}

.table-row {
  display: flex;
  border-bottom: 1px solid #f0f0f0;
  transition: background-color 0.2s;
}

.table-row:hover {
  background: #f8f9fa;
}

.table-cell {
  padding: 12px;
  flex: 1;
  min-width: 100px;
  font-size: 14px;
  color: #333 !important;
  overflow: hidden;
  text-overflow: ellipsis;
}

.table-cell:first-child {
  min-width: 180px;
  white-space: nowrap;
}

.table-cell:nth-child(2) {
  min-width: 80px;
  white-space: nowrap;
}

.table-cell:nth-child(3) {
  min-width: 100px;
  white-space: nowrap;
}

.table-cell:nth-child(4) {
  min-width: 80px;
  white-space: nowrap;
}

.table-cell:nth-child(5) {
  min-width: 200px;
  max-width: 300px;
  word-break: break-word;
}

.table-cell:nth-child(6) {
  min-width: 60px;
  white-space: nowrap;
}

.table-cell:nth-child(7) {
  min-width: 120px;
  white-space: nowrap;
}

.operation-login {
  color: #409eff !important;
  font-weight: 500;
}

.operation-failed {
  color: #f56c6c !important;
  font-weight: 500;
}

.operation-manage {
  color: #67c23a !important;
  font-weight: 500;
}

.operation-other {
  color: #909399 !important;
  font-weight: 500;
}

.success {
  color: #67c23a !important;
  font-weight: 500;
}

.failed {
  color: #f56c6c !important;
  font-weight: 500;
}

.no-logs {
  text-align: center;
  padding: 40px;
  color: #999 !important;
}

.no-logs i {
  font-size: 48px;
  margin-bottom: 10px;
  display: block;
  color: #ccc !important;
}

.no-logs p {
  color: #999 !important;
  margin: 0;
}

.logs-pagination {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 20px;
  padding: 20px;
}

.page-btn {
  padding: 8px 16px;
  border: 1px solid #ddd;
  border-radius: 4px;
  background: white;
  color: #333 !important;
  cursor: pointer;
  font-size: 14px;
  transition: all 0.2s;
}

.page-btn:hover:not(:disabled) {
  background: #f0f9ff;
  border-color: #409eff;
  color: #409eff !important;
}

.page-btn:disabled {
  background: #f5f5f5;
  color: #ccc !important;
  cursor: not-allowed;
}

.page-info {
  font-size: 14px;
  color: #666 !important;
}

/* 搜索输入框样式 */
.search-input {
  padding: 8px 12px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 14px;
  width: 200px;
  margin-right: 10px;
}

.search-input:focus {
  outline: none;
  border-color: #409eff;
}

/* 文本颜色类 */
.text-success {
  color: #67c23a !important;
  font-weight: 500;
}

.text-danger {
  color: #f56c6c !important;
  font-weight: 500;
}

/* 客户端ID列样式 - 小字体，防止换行 */
.client-id-cell {
  font-size: 12px !important;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace !important;
  white-space: nowrap !important;
  overflow: hidden !important;
  text-overflow: ellipsis !important;
}

.client-id-cell .cell {
  font-size: 12px !important;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace !important;
  white-space: nowrap !important;
  overflow: hidden !important;
  text-overflow: ellipsis !important;
  padding: 8px 12px !important;
}
</style> 