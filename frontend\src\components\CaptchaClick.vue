<template>
  <div class="app-container">
    <!-- 页面标题 (仅在未登录时显示) -->
    <div v-if="!isLoggedIn" class="page-header">
      <h1 class="page-title">
        <i class="fas fa-desktop"></i>
        终端管理系统
      </h1>
      <p class="page-subtitle">IPC Management Platform</p>
    </div>

    <!-- 登录区域 -->
    <div v-if="!isLoggedIn" class="login-section">
      <div class="login-card">
        <div class="login-header">
          <div class="login-icon">
            <i class="fas fa-shield-alt"></i>
          </div>
          <h2>用户登录</h2>
          <p>请输入您的账号信息进行登录</p>
        </div>
        <el-form :model="loginForm" class="login-form" size="large">
          <el-form-item>
            <el-input
              v-model="loginForm.username"
              placeholder="请输入用户名"
              clearable
              size="large"
            >
              <template #prefix>
                <i class="fas fa-user"></i>
              </template>
            </el-input>
          </el-form-item>
          <el-form-item>
            <el-input
              v-model="loginForm.password"
              type="password"
              placeholder="请输入密码"
              clearable
              show-password
              size="large"
            >
              <template #prefix>
                <i class="fas fa-lock"></i>
              </template>
            </el-input>
          </el-form-item>
          <el-form-item>
            <el-button 
              type="primary" 
              @click="handleLogin" 
              :loading="loginLoading"
              class="login-button"
            >
              {{ loginLoading ? '登录中...' : '登录' }}
            </el-button>
          </el-form-item>
        </el-form>
      </div>
    </div>
      
    <!-- 现代化后台管理页面 -->
    <div v-if="isLoggedIn" class="modern-layout">
        <!-- 移动端菜单按钮 -->
        <button v-if="isMobile" @click="toggleSidebar" class="mobile-menu-btn">
          <i class="fas fa-bars"></i>
        </button>

        <!-- 移动端遮罩层 -->
        <div
          v-if="isMobile && sidebarOpen"
          class="mobile-overlay show active"
          @click="closeSidebar"
        ></div>

        <!-- 现代化侧边栏 -->
        <div class="modern-sidebar" :class="{ 'mobile-open': isMobile && sidebarOpen }">
          <div class="sidebar-brand">
            <h1>终端管理</h1>
            <div class="version">v2.0</div>
          </div>

          <nav class="modern-nav">
            <a href="#" class="nav-item" :class="{ active: activeMenu === 'dashboard' }" @click.prevent="handleMenuSelect('dashboard')">
              <i class="fas fa-tachometer-alt"></i>
              <span>仪表盘</span>
            </a>
            <a href="#" class="nav-item" :class="{ active: activeMenu === 'users' }" @click.prevent="handleMenuSelect('users')">
              <i class="fas fa-users"></i>
              <span>用户管理</span>
            </a>
            <a href="#" class="nav-item" :class="{ active: activeMenu === 'devices' }" @click.prevent="handleMenuSelect('devices')">
              <i class="fas fa-mobile-alt"></i>
              <span>设备管理</span>
            </a>
            <a href="#" class="nav-item" :class="{ active: activeMenu === 'mqtt' }" @click.prevent="handleMenuSelect('mqtt')">
              <i class="fas fa-network-wired"></i>
              <span>MQTT管理</span>
            </a>
            <a href="#" class="nav-item" :class="{ active: activeMenu === 'logs' }" @click.prevent="handleMenuSelect('logs')">
              <i class="fas fa-clipboard-list"></i>
              <span>操作日志</span>
            </a>
            <a href="#" class="nav-item" :class="{ active: activeMenu === 'settings' }" @click.prevent="handleMenuSelect('settings')">
              <i class="fas fa-cog"></i>
              <span>系统设置</span>
            </a>
          </nav>
        </div>

        <!-- 现代化主内容区域 -->
        <div class="modern-main">
          <!-- 现代化顶部导航栏 -->
          <div class="modern-header">
            <h1 class="header-title">{{ getMenuTitle(activeMenu) }}</h1>
            <div class="header-actions">
              <div class="user-info">
                <div class="user-avatar">{{ username.charAt(0).toUpperCase() }}</div>
                <span class="user-name">{{ username }}</span>
              </div>
              <button class="modern-btn btn-danger" @click="logout">
                <i class="fas fa-sign-out-alt"></i>
                退出登录
              </button>
            </div>
          </div>

          <!-- 现代化内容区域 -->
          <div class="modern-content">
            <!-- 仪表盘 -->
            <div v-if="activeMenu === 'dashboard'">
              <!-- 统计卡片网格 -->
              <div class="stats-grid">
                <div class="stat-card">
                  <div class="stat-header">
                    <span class="stat-label">当前用户</span>
                    <div class="stat-icon primary">
                      <i class="fas fa-user"></i>
                    </div>
                  </div>
                  <div class="stat-value">{{ username }}</div>
                </div>

                <div class="stat-card">
                  <div class="stat-header">
                    <span class="stat-label">总用户数</span>
                    <div class="stat-icon success">
                      <i class="fas fa-users"></i>
                    </div>
                  </div>
                  <div class="stat-value">{{ totalUsers }}</div>
                </div>

                <div class="stat-card">
                  <div class="stat-header">
                    <span class="stat-label">系统状态</span>
                    <div class="stat-icon success">
                      <i class="fas fa-check-circle"></i>
                    </div>
                  </div>
                  <div class="stat-value">运行中</div>
                </div>

                <div class="stat-card">
                  <div class="stat-header">
                    <span class="stat-label">MQTT状态</span>
                    <div :class="['stat-icon', mqttStats.running ? 'success' : 'danger']">
                      <i class="fas fa-wifi"></i>
                    </div>
                  </div>
                  <div class="stat-value">{{ mqttStats.running ? '运行中' : '已停止' }}</div>
                </div>
              </div>

              <!-- 功能卡片 -->
              <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 2rem;">
                <!-- 系统概览 -->
                <div class="modern-card">
                  <div class="card-header">
                    <h3 class="card-title">
                      <i class="fas fa-server"></i>
                      系统概览
                    </h3>
                  </div>
                  <div class="card-content">
                    <div style="display: flex; flex-direction: column; gap: 1rem;">
                      <div style="display: flex; justify-content: space-between; align-items: center; padding: 0.75rem 0; border-bottom: 1px solid var(--border-light);">
                        <span style="color: var(--text-secondary); font-weight: 500;">应用版本:</span>
                        <span style="color: var(--text-primary); font-weight: 600;">v1.0</span>
                      </div>
                      <div style="display: flex; justify-content: space-between; align-items: center; padding: 0.75rem 0; border-bottom: 1px solid var(--border-light);">
                        <span style="color: var(--text-secondary); font-weight: 500;">运行时间:</span>
                        <span style="color: var(--success-color); font-weight: 600;">正常运行</span>
                      </div>
                      <div style="display: flex; justify-content: space-between; align-items: center; padding: 0.75rem 0;">
                        <span style="color: var(--text-secondary); font-weight: 500;">数据库状态:</span>
                        <span style="color: var(--success-color); font-weight: 600;">连接正常</span>
                      </div>
                    </div>
                  </div>
                </div>

                <!-- 快速操作 -->
                <div class="modern-card">
                  <div class="card-header">
                    <h3 class="card-title">
                      <i class="fas fa-bolt"></i>
                      快速操作
                    </h3>
                  </div>
                  <div class="card-content">
                    <div style="display: flex; flex-direction: column; gap: 0.75rem;">
                      <button @click="handleMenuSelect('users')" class="modern-btn quick-action-btn">
                        <i class="fas fa-users"></i>
                        用户管理
                      </button>
                      <button @click="handleMenuSelect('mqtt')" class="modern-btn quick-action-btn">
                        <i class="fas fa-network-wired"></i>
                        MQTT管理
                      </button>
                      <button @click="handleMenuSelect('logs')" class="modern-btn quick-action-btn">
                        <i class="fas fa-clipboard-list"></i>
                        操作日志
                      </button>
                    </div>
                  </div>
                </div>
              </div>
            </div>


            <!-- 用户管理 -->
            <div v-else-if="activeMenu === 'users'" class="user-management">
              <!-- 操作工具栏 -->
              <div class="modern-card" style="margin-bottom: 1rem;">
                <div class="card-header">
                  <h3 class="card-title">
                    <i class="fas fa-users"></i>
                    用户列表
                  </h3>
                  <div style="display: flex; gap: 1rem; align-items: center; flex-wrap: wrap;">
                    <div class="modern-search">
                      <i class="fas fa-search"></i>
                      <input
                        v-model="searchKeyword"
                        @input="handleSearch"
                        placeholder="搜索用户名..."
                        class="search-input"
                      />
                    </div>
                    
                    <!-- 批量操作按钮组 -->
                    <div class="batch-operations" style="display: flex; gap: 0.5rem; align-items: center;">
                      <button 
                        @click="batchToggleUsers('enable')" 
                        class="modern-btn success small"
                        :disabled="selectedUsers.length === 0 || batchActionLoading"
                        :title="selectedUsers.length === 0 ? '请先选择用户' : `批量启用 ${selectedUsers.length} 个用户`"
                      >
                        <i class="fas fa-check"></i>
                        {{ batchActionLoading ? '处理中...' : '批量启用' }}
                      </button>
                      <button 
                        @click="batchToggleUsers('disable')" 
                        class="modern-btn warning small"
                        :disabled="selectedUsers.length === 0 || batchActionLoading"
                        :title="selectedUsers.length === 0 ? '请先选择用户' : `批量禁用 ${selectedUsers.length} 个用户`"
                      >
                        <i class="fas fa-ban"></i>
                        {{ batchActionLoading ? '处理中...' : '批量禁用' }}
                      </button>
                    </div>
                    
                    <!-- 分隔线 -->
                    <div style="height: 30px; width: 1px; background: #e5e7eb; margin: 0 0.5rem;"></div>
                    
                    <!-- 添加用户按钮 -->
                    <button @click="showAddUserDialog" class="modern-btn primary">
                      <i class="fas fa-plus"></i>
                      添加用户
                    </button>
                  </div>
                </div>
              </div>

              <!-- 用户表格 -->
              <div class="modern-card">
                <div class="modern-table-container">
                <el-table
                  :data="filteredUsers"
                  border
                  stripe
                  style="width: 100%"
                  v-loading="usersLoading"
                  :height="'calc(100vh - 340px)'"
                  @selection-change="handleSelectionChange"
                >
                  <el-table-column type="selection" width="55" :selectable="row => row.username !== 'admin'" />
                  <el-table-column prop="username" label="用户名" min-width="120" />
                  <el-table-column label="后台权限" min-width="100">
                    <template #default="scope">
                      <el-tag :type="scope.row.can_login_backend ? 'success' : 'danger'" size="small">
                        {{ scope.row.can_login_backend ? '是' : '否' }}
                      </el-tag>
                    </template>
                  </el-table-column>
                  <el-table-column label="MQTT权限" min-width="100">
                    <template #default="scope">
                      <el-tag :type="scope.row.can_login_mqtt ? 'success' : 'danger'" size="small">
                        {{ scope.row.can_login_mqtt ? '是' : '否' }}
                      </el-tag>
                    </template>
                  </el-table-column>
                  <el-table-column prop="is_active" label="状态" min-width="80">
                    <template #default="scope">
                      <el-tag :type="scope.row.is_active ? 'success' : 'danger'" size="small">
                        {{ scope.row.is_active ? '正常' : '禁用' }}
                      </el-tag>
                    </template>
                  </el-table-column>
                  <el-table-column prop="remark" label="备注" min-width="150">
                    <template #default="scope">
                      <span :title="scope.row.remark">{{ scope.row.remark || '-' }}</span>
                    </template>
                  </el-table-column>
                  <el-table-column label="操作" min-width="180">
                    <template #default="scope">
                      <div style="display: flex; gap: 8px; flex-wrap: nowrap;">
                        <button class="modern-btn secondary small" @click="editUser(scope.row)">
                          <i class="fas fa-edit"></i>
                          编辑
                        </button>
                        <el-tooltip
                          :content="getDeleteButtonTooltip(scope.row)"
                          placement="top"
                          :disabled="scope.row.username !== username && scope.row.username !== 'admin'"
                        >
                          <button
                            class="modern-btn danger small"
                            @click="deleteUser(scope.row)"
                            :disabled="scope.row.username === username || scope.row.username === 'admin'"
                          >
                            <i class="fas fa-trash"></i>
                            删除
                          </button>
                        </el-tooltip>
                      </div>
                    </template>
                  </el-table-column>
                </el-table>
                
                <!-- 分页组件 - 始终显示 -->
                <div class="user-pagination">
                  <el-pagination
                    v-model:current-page="userPagination.page"
                    v-model:page-size="userPagination.pageSize"
                    :page-sizes="[10, 20, 50, 100]"
                    :total="userPagination.total"
                    layout="total, sizes, prev, pager, next, jumper"
                    @size-change="handlePageSizeChange"
                    @current-change="handlePageChange"
                    :small="false"
                    background
                    :hide-on-single-page="false"
                  />
                </div>
                </div>
              </div>
            </div>

            <!-- 设备管理 -->
            <div v-else-if="activeMenu === 'devices'" class="device-management">
              <!-- 设备管理子标签页 -->
              <el-tabs v-model="activeDeviceSubTab" class="device-sub-tabs">
                <el-tab-pane label="设备列表" name="device-list">
                  <div class="device-list-content">
                    <!-- 统计卡片区域 -->
                    <div class="device-stats-cards">
                      <div class="stat-card">
                        <div class="stat-icon primary">
                          <i class="fas fa-mobile-alt"></i>
                        </div>
                        <div class="stat-content">
                          <div class="stat-value">{{ Array.isArray(devices) ? devices.length : 0 }}</div>
                          <div class="stat-label">设备总数</div>
                        </div>
                      </div>
                      <div class="stat-card">
                        <div class="stat-icon success">
                          <i class="fas fa-wifi"></i>
                        </div>
                        <div class="stat-content">
                          <div class="stat-value">{{ Array.isArray(devices) ? devices.filter(d => isDeviceOnline(d.device_id)).length : 0 }}</div>
                          <div class="stat-label">在线设备</div>
                        </div>
                      </div>
                      <div class="stat-card">
                        <div class="stat-icon danger">
                          <i class="fas fa-times-circle"></i>
                        </div>
                        <div class="stat-content">
                          <div class="stat-value">{{ Array.isArray(devices) ? devices.filter(d => !isDeviceOnline(d.device_id)).length : 0 }}</div>
                          <div class="stat-label">离线设备</div>
                        </div>
                      </div>
                      <div class="stat-card">
                        <div class="stat-icon success">
                          <i class="fas fa-check-circle"></i>
                        </div>
                        <div class="stat-content">
                          <div class="stat-value">{{ Array.isArray(devices) ? devices.filter(d => d.is_active).length : 0 }}</div>
                          <div class="stat-label">启用设备</div>
                        </div>
                      </div>
                      <div class="stat-card">
                        <div class="stat-icon warning">
                          <i class="fas fa-ban"></i>
                        </div>
                        <div class="stat-content">
                          <div class="stat-value">{{ Array.isArray(devices) ? devices.filter(d => !d.is_active).length : 0 }}</div>
                          <div class="stat-label">禁用设备</div>
                        </div>
                      </div>
                    </div>

                    <!-- 设备列表标题 -->
                    <div class="section-header">
                      <h3 class="section-title">
                        <i class="fas fa-list"></i>
                        设备列表
                      </h3>
                    </div>

                    <!-- 操作工具栏 -->
                    <div class="modern-card device-toolbar">
                      <div class="modern-search">
                        <i class="fas fa-search"></i>
                        <input
                          v-model="deviceSearchKeyword"
                          @input="handleDeviceSearch"
                          placeholder="搜索设备ID或名称..."
                          class="search-input"
                        />
                      </div>
                      <div class="toolbar-buttons">
                        <button
                          class="modern-btn secondary"
                          @click="loadDevices"
                          :disabled="devicesLoading"
                          title="刷新数据"
                        >
                          <i class="fas fa-sync" :class="{ 'fa-spin': devicesLoading }"></i>
                          刷新
                        </button>
                        <button
                          class="modern-btn primary"
                          @click="showCreateDeviceDialog"
                          title="添加新设备"
                        >
                          <i class="fas fa-plus"></i>
                          添加设备
                        </button>
                      </div>
                    </div>

                    <!-- 批量操作栏 -->
                    <div v-if="selectedDeviceIds.length > 0" class="batch-operations-bar">
                      <div class="batch-info">
                        <i class="fas fa-check-square"></i>
                        已选择 {{ selectedDeviceIds.length }} 个设备
                      </div>
                      <div class="batch-actions">
                        <button
                          class="modern-btn btn-success btn-sm"
                          @click="batchToggleDevices(true)"
                          title="批量启用选中设备"
                        >
                          <i class="fas fa-play"></i>
                          批量启用
                        </button>
                        <button
                          class="modern-btn btn-warning btn-sm"
                          @click="batchToggleDevices(false)"
                          title="批量禁用选中设备"
                        >
                          <i class="fas fa-pause"></i>
                          批量禁用
                        </button>
                        <button
                          class="modern-btn btn-secondary btn-sm"
                          @click="selectedDeviceIds = []"
                          title="取消选择"
                        >
                          <i class="fas fa-times"></i>
                          取消选择
                        </button>
                      </div>
                    </div>

                    <!-- 设备列表表格 -->
                    <div v-if="devicesLoading" class="loading-container">
                      <div class="loading-spinner"></div>
                      <span>加载中...</span>
                    </div>

                    <p v-else-if="!devices.length" style="text-align: center; color: #999; padding: 40px;">暂无设备，点击上方按钮添加第一个设备</p>

                    <div v-else class="modern-table-container">
                    <table class="modern-table">
                      <thead>
                        <tr>
                          <th style="width: 50px;">
                            <input
                              type="checkbox"
                              :checked="isAllDevicesSelected"
                              @change="toggleAllDevicesSelection"
                              class="modern-checkbox"
                            />
                          </th>
                          <th style="width: 120px;">
                            客户端ID
                          </th>
                          <th style="width: 140px;">设备名称</th>
                          <th style="width: 100px;">所属用户</th>
                          <th style="width: 80px;">设备类型</th>
                          <th style="width: 110px;">授权状态</th>
                          <th style="width: 120px;">使用统计</th>
                          <th style="width: 70px;">设备状态</th>
                          <th style="width: 70px;">连接状态</th>
                          <th style="width: 140px;">创建时间</th>
                          <th style="width: 200px;">操作</th>
                        </tr>
                      </thead>
                      <tbody>
                        <tr v-for="device in filteredDevices" :key="device.device_id">
                          <td>
                            <input
                              type="checkbox"
                              :value="device.device_id"
                              v-model="selectedDeviceIds"
                              class="modern-checkbox"
                            />
                          </td>
                          <td>
                            <code class="device-id">{{ device.device_id }}</code>
                          </td>
                          <td>
                            <div class="device-name">
                              <i class="fas fa-mobile-alt"></i>
                              {{ device.device_name }}
                              <span v-if="device.device_name && device.device_name.startsWith('自动创建设备_')"
                                    class="auto-created-badge"
                                    title="此设备由MQTT客户端登录时自动创建">
                                <i class="fas fa-magic"></i>
                                自动
                              </span>
                            </div>
                          </td>
                          <td>
                            <div class="user-info">
                              <i class="fas fa-user"></i>
                              {{ device.username }}
                            </div>
                          </td>
                          <td>
                            <span class="device-type-icon-only" :title="getDeviceTypeName(device.device_type)">
                              <i :class="getDeviceTypeIcon(device.device_type)" :style="{ color: getDeviceTypeColor(device.device_type) }"></i>
                            </span>
                          </td>
                          <td>
                            <span class="auth-badge" :class="getAuthStatusType(device)">
                              {{ getAuthStatusText(device) }}
                            </span>
                          </td>
                          <td>
                            <div v-if="device.authorization" class="usage-stats">
                              <div class="usage-bar">
                                <div 
                                  class="usage-fill" 
                                  :style="{ width: getUsagePercentage(device.authorization) + '%' }"
                                  :class="{ 'usage-warning': getUsagePercentage(device.authorization) > 80 }"
                                ></div>
                              </div>
                              <span class="usage-text">
                                {{ device.authorization.current_usage }} / {{ device.authorization.max_usage }}
                              </span>
                            </div>
                            <span v-else>-</span>
                          </td>
                          <td>
                            <span class="status-badge" :class="device.is_active ? 'status-active' : 'status-inactive'">
                              {{ device.is_active ? '启用' : '禁用' }}
                            </span>
                          </td>
                          <td>
                            <span class="status-badge" :class="isDeviceOnline(device.device_id) ? 'status-active' : 'status-inactive'">
                              {{ isDeviceOnline(device.device_id) ? '在线' : '离线' }}
                            </span>
                          </td>
                          <td>{{ formatDateTime(device.created_at) }}</td>
                          <td>
                            <div class="action-buttons">
                              <button
                                class="modern-btn btn-primary btn-sm"
                                @click="showEditDeviceDialog(device)"
                                title="编辑设备"
                              >
                                <i class="fas fa-edit"></i>
                              </button>
                              <button
                                v-if="device.authorization"
                                class="modern-btn btn-info btn-sm"
                                @click="resetDeviceUsage(device)"
                                title="重置使用次数"
                              >
                                <i class="fas fa-undo"></i>
                              </button>
                              <button
                                class="modern-btn btn-danger btn-sm"
                                @click="deleteDevice(device)"
                                title="删除设备"
                              >
                                <i class="fas fa-trash"></i>
                              </button>
                            </div>
                          </td>
                        </tr>
                      </tbody>
                    </table>
                    </div>
                  </div>
                </el-tab-pane>

                <!-- 设备类型管理标签页 -->
                <el-tab-pane label="设备类型" name="device-types">
                  <div class="device-types-content">
                    <div class="modern-card">
                      <div class="card-header">
                        <h3 class="card-title">
                          <i class="fas fa-tags"></i>
                          设备类型管理
                        </h3>
                        <div class="card-actions">
                          <button class="modern-btn primary" @click="showAddDeviceTypeDialog">
                            <i class="fas fa-plus"></i>
                            新增类型
                          </button>
                          <button
                            v-if="hasUserDefinedDeviceTypes"
                            class="modern-btn danger"
                            @click="clearAllDeviceTypes"
                          >
                            <i class="fas fa-trash-alt"></i>
                            清空类型
                          </button>
                        </div>
                      </div>

                      <div class="card-content">
                        <div v-if="deviceTypesLoading" class="loading-state">
                          <i class="fas fa-spinner fa-spin"></i>
                          加载中...
                        </div>
                        <div v-else-if="deviceTypes.length > 0" class="device-types-grid">
                          <div
                            v-for="deviceType in deviceTypes"
                            :key="deviceType.id"
                            class="device-type-card"
                            :class="{ 'system-type': deviceType.is_system, 'inactive': !deviceType.is_active }"
                          >
                            <div class="type-header">
                              <div class="type-icon" :style="{ color: deviceType.color }">
                                <i :class="deviceType.icon"></i>
                              </div>
                              <div class="type-info">
                                <h4 class="type-name">{{ deviceType.type_name }}</h4>
                                <span class="type-code">{{ deviceType.type_code }}</span>
                              </div>
                              <div class="type-status">
                                <span v-if="deviceType.is_system" class="system-badge">系统</span>
                                <span v-if="!deviceType.is_active" class="inactive-badge">禁用</span>
                              </div>
                            </div>
                            <div class="type-description">
                              {{ deviceType.description || '暂无描述' }}
                            </div>
                            <div class="type-actions">
                              <button
                                v-if="!deviceType.is_system"
                                class="modern-btn small primary"
                                @click="showEditDeviceTypeDialog(deviceType)"
                              >
                                <i class="fas fa-edit"></i>
                                编辑
                              </button>
                              <button
                                v-if="!deviceType.is_system"
                                class="modern-btn small danger"
                                @click="deleteDeviceType(deviceType)"
                              >
                                <i class="fas fa-trash"></i>
                                删除
                              </button>
                              <span v-if="deviceType.is_system" class="system-type-hint">
                                系统保护类型
                              </span>
                            </div>
                          </div>
                        </div>
                        <p v-else style="text-align: center; color: #999; padding: 20px;">暂无设备类型</p>
                      </div>
                    </div>
                  </div>
                </el-tab-pane>
              </el-tabs>
            </div>

            <!-- MQTT服务器管理 -->
            <div v-else-if="activeMenu === 'mqtt'" class="mqtt-management">
              <!-- MQTT统计信息 -->
              <div class="stats-grid">
                <div class="stat-card">
                  <div class="stat-header">
                    <span class="stat-label">服务器状态</span>
                    <div class="stat-icon" :class="mqttStats.running ? 'success' : 'danger'">
                      <i :class="mqttStats.running ? 'fas fa-check' : 'fas fa-times'"></i>
                    </div>
                  </div>
                  <div class="stat-value" :class="mqttStats.running ? 'text-success' : 'text-danger'">
                    {{ mqttStats.running ? '运行中' : '已停止' }}
                  </div>
                </div>

                <div class="stat-card">
                  <div class="stat-header">
                    <span class="stat-label">连接客户端</span>
                    <div class="stat-icon primary">
                      <i class="fas fa-users"></i>
                    </div>
                  </div>
                  <div class="stat-value">{{ mqttStats.clients || 0 }}</div>
                </div>

                <div class="stat-card">
                  <div class="stat-header">
                    <span class="stat-label">订阅主题</span>
                    <div class="stat-icon warning">
                      <i class="fas fa-list"></i>
                    </div>
                  </div>
                  <div class="stat-value">{{ mqttStats.topics || 0 }}</div>
                </div>

                <div class="stat-card">
                  <div class="stat-header">
                    <span class="stat-label">黑名单数量</span>
                    <div class="stat-icon danger">
                      <i class="fas fa-ban"></i>
                    </div>
                  </div>
                  <div class="stat-value">{{ mqttStats.blacklist_count || 0 }}</div>
                </div>
              </div>

              <!-- MQTT内容区域 -->
              <el-tabs v-model="mqttActiveTab" type="card" class="mqtt-tabs">
                  <el-tab-pane label="连接管理" name="clients">
                    <div class="mqtt-section">
                      <div class="section-header">
                        <h4><i class="fas fa-plug"></i> 客户端列表</h4>
                      </div>
                      <div v-if="mqttStats.client_list && mqttStats.client_list.length > 0">
                        <el-table
                          :data="mqttStats.client_list"
                          border
                          stripe
                          class="standard-table"
                          size="small"
                          :height="'auto'"
                          :max-height="400"
                        >
                          <el-table-column prop="id" label="客户端ID" width="200" class-name="client-id-cell" show-overflow-tooltip />
                          <el-table-column prop="username" label="用户名" width="90" show-overflow-tooltip />
                          <el-table-column prop="client_ip" label="IP地址" width="120">
                            <template #default="scope">
                              {{ formatIpAddress(scope.row.client_ip) }}
                            </template>
                          </el-table-column>
                          <el-table-column prop="authenticated" label="状态" width="80">
                            <template #default="scope">
                              <el-tag :type="scope.row.authenticated ? 'success' : 'danger'" size="small">
                                {{ scope.row.authenticated ? '认证' : '未认证' }}
                              </el-tag>
                            </template>
                          </el-table-column>
                          <el-table-column label="订阅主题" min-width="200">
                            <template #default="scope">
                              <div v-if="scope.row.subscribed_topics && scope.row.subscribed_topics.length > 0">
                                <el-tag
                                  v-for="topic in scope.row.subscribed_topics"
                                  :key="topic"
                                  size="small"
                                  type="info"
                                  style="margin: 2px; display: block; width: fit-content;"
                                >
                                  {{ topic }}
                                </el-tag>
                              </div>
                              <span v-else style="color: #909399; font-size: 12px;">无订阅</span>
                            </template>
                          </el-table-column>
                          <el-table-column label="最后活跃" width="120">
                            <template #default="scope">
                              <div style="font-size: 11px; color: #606266;">
                                {{ formatLastPing(scope.row.last_ping) }}
                              </div>
                            </template>
                          </el-table-column>
                          <el-table-column label="操作" width="100" align="center">
                            <template #default="scope">
                              <el-button
                                size="small"
                                type="primary"
                                @click="showHeartbeatMonitor(scope.row.id)"
                              >
                                <i class="el-icon-monitor"></i>
                                心跳
                              </el-button>
                            </template>
                          </el-table-column>
                        </el-table>
                      </div>
                      <p v-else style="text-align: center; color: #999; padding: 20px;">暂无客户端连接</p>
                    </div>
                  </el-tab-pane>
                  
                  <el-tab-pane label="安全管理" name="security">
                    <div class="security-sections">
                      <!-- 黑名单管理 -->
                      <div class="mqtt-section">
                        <div class="section-header">
                          <h4><i class="el-icon-warning"></i> 黑名单管理</h4>
                          <button class="add-btn" @click="showAddBlacklistDialog">
                            <i class="el-icon-plus"></i>
                            添加黑名单
                          </button>
                        </div>
                        <div v-if="mqttStats.blacklist && mqttStats.blacklist.length > 0">
                          <el-table
                            :data="mqttStats.blacklist"
                            border
                            stripe
                            class="standard-table"
                            size="small"
                            :height="'auto'"
                            :max-height="300"
                          >
                            <el-table-column prop="ip" label="IP地址" width="140">
                              <template #default="scope">
                                {{ formatIpAddress(scope.row.ip) }}
                              </template>
                            </el-table-column>
                            <el-table-column prop="username" label="用户名" width="120" />
                            <el-table-column prop="reason" label="封禁原因" />
                            <el-table-column prop="added_at" label="添加时间" width="160">
                              <template #default="scope">
                                {{ formatDate(scope.row.added_at) }}
                              </template>
                            </el-table-column>
                            <el-table-column prop="expires_at" label="过期时间" width="160">
                              <template #default="scope">
                                {{ formatDate(scope.row.expires_at) }}
                              </template>
                            </el-table-column>
                            <el-table-column label="操作" width="100">
                              <template #default="scope">
                                <el-button size="small" type="danger" @click="removeFromBlacklist(scope.row.ip)">
                                  移除
                                </el-button>
                              </template>
                            </el-table-column>
                          </el-table>
                        </div>
                        <p v-else style="text-align: center; color: #999; padding: 20px;">暂无黑名单记录</p>
                      </div>
                      
                      <!-- 失败认证记录 -->
                      <div class="mqtt-section">
                        <div class="section-header">
                          <h4><i class="el-icon-lock"></i> 失败认证记录</h4>
                        </div>
                        <div v-if="mqttStats.failed_auth && mqttStats.failed_auth.length > 0">
                          <el-table
                            :data="mqttStats.failed_auth"
                            border
                            stripe
                            class="standard-table"
                            size="small"
                            :height="'auto'"
                            :max-height="300"
                          >
                            <el-table-column prop="ip" label="IP地址" width="140">
                              <template #default="scope">
                                {{ formatIpAddress(scope.row.ip) }}
                              </template>
                            </el-table-column>
                            <el-table-column prop="username" label="用户名" width="120" />
                            <el-table-column prop="count" label="失败次数" width="100" />
                            <el-table-column prop="last_attempt" label="最后尝试" width="160">
                              <template #default="scope">
                                {{ formatDate(scope.row.last_attempt) }}
                              </template>
                            </el-table-column>
                            <el-table-column label="操作" width="100">
                              <template #default="scope">
                                <el-button size="small" type="warning" @click="clearFailedAuth(scope.row.ip)">
                                  清除
                                </el-button>
                              </template>
                            </el-table-column>
                          </el-table>
                        </div>
                        <p v-else style="text-align: center; color: #999; padding: 20px;">暂无失败认证记录</p>
                      </div>
                    </div>
                  </el-tab-pane>
                  
                  <el-tab-pane label="配置管理" name="config">
                    <div class="mqtt-config-container">
                      <!-- 认证说明 - 紧凑版 -->
                      <div class="config-info-banner">
                        <i class="el-icon-info-filled"></i>
                        <span>MQTT服务器使用系统用户账号进行认证，只有启用状态的用户才能连接</span>
                      </div>
                      
                      <!-- 配置表单 - 两列布局 -->
                      <div class="config-grid">
                        <el-form :model="mqttConfig" label-width="140px" class="config-form-compact">
                          
                          <!-- 左列：认证与安全 -->
                          <div class="config-column">
                            <div class="config-group">
                              <h4 class="config-group-title">
                                <i class="el-icon-lock"></i>认证与安全
                              </h4>
                              <el-form-item label="启用认证">
                                <el-switch
                                  v-model="mqttConfig.require_auth"
                                  @focus="startEditing('require_auth')"
                                  @blur="stopEditing('require_auth')"
                                />
                              </el-form-item>
                              <el-form-item label="允许匿名连接">
                                <el-switch
                                  v-model="mqttConfig.allow_anonymous"
                                  @focus="startEditing('allow_anonymous')"
                                  @blur="stopEditing('allow_anonymous')"
                                />
                              </el-form-item>
                              <el-form-item label="最大失败次数">
                                <div class="input-with-unit-compact">
                                  <el-input-number
                                    v-model="mqttConfig.max_failed_attempts"
                                    :min="1" :max="10" size="small"
                                    @focus="startEditing('max_failed_attempts')"
                                    @blur="stopEditing('max_failed_attempts')"
                                  />
                                  <span class="unit">次</span>
                                </div>
                              </el-form-item>
                              <el-form-item label="封禁时长">
                                <div class="input-with-unit-compact">
                                  <el-input-number
                                    v-model="mqttConfig.blacklist_duration"
                                    :min="1" :max="168" size="small"
                                    @focus="startEditing('blacklist_duration')"
                                    @blur="stopEditing('blacklist_duration')"
                                  />
                                  <span class="unit">小时</span>
                                </div>
                              </el-form-item>
                              <el-form-item label="清理间隔">
                                <div class="input-with-unit-compact">
                                  <el-input-number
                                    v-model="mqttConfig.cleanup_interval"
                                    :min="1" :max="1440" size="small"
                                    @focus="startEditing('cleanup_interval')"
                                    @blur="stopEditing('cleanup_interval')"
                                  />
                                  <span class="unit">分钟</span>
                                </div>
                              </el-form-item>
                            </div>
                          </div>

                          <!-- 右列：心跳与遗嘱 -->
                          <div class="config-column">
                            <div class="config-group">
                              <h4 class="config-group-title">
                                <i class="el-icon-time"></i>心跳与遗嘱
                              </h4>
                              <el-form-item label="心跳检查间隔">
                                <div class="input-with-unit-compact">
                                  <el-input-number
                                    v-model="mqttConfig.heartbeat_interval"
                                    :min="5" :max="300" size="small"
                                    @focus="startEditing('heartbeat_interval')"
                                    @blur="stopEditing('heartbeat_interval')"
                                  />
                                  <span class="unit">秒</span>
                                </div>
                              </el-form-item>
                              <el-form-item label="心跳超时时间">
                                <div class="input-with-unit-compact">
                                  <el-input-number
                                    v-model="mqttConfig.heartbeat_timeout"
                                    :min="30" :max="600" size="small"
                                    @focus="startEditing('heartbeat_timeout')"
                                    @blur="stopEditing('heartbeat_timeout')"
                                  />
                                  <span class="unit">秒</span>
                                </div>
                              </el-form-item>
                              <el-form-item label="心跳包间隔">
                                <div class="input-container">
                                  <div>
                                    <el-input-number
                                      v-model="mqttConfig.heartbeat_packet_interval"
                                      :min="500" :max="60000" size="small"
                                      @focus="startEditing('heartbeat_packet_interval')"
                                      @blur="stopEditing('heartbeat_packet_interval')"
                                    />
                                    <span style="margin-left: 10px; color: #999;">毫秒</span>
                                  </div>
                                  <div class="config-help">
                                    <span class="range">安全范围: 500-60000</span>
                                    <div class="desc">客户端心跳包发送的最小间隔时间，低于此值将被加入黑名单</div>
                                  </div>
                                </div>
                              </el-form-item>

                              <!-- 遗嘱时间预估 - 紧凑版 -->
                              <div class="timing-preview-compact">
                                <div class="timing-title-compact">
                                  <i class="el-icon-info"></i>遗嘱触发时间预估
                                </div>
                                <div class="timing-items">
                                  <div class="timing-item-compact">
                                    <span>最快：{{ mqttConfig.heartbeat_timeout }}秒</span>
                                  </div>
                                  <div class="timing-item-compact">
                                    <span>最慢：{{ mqttConfig.heartbeat_timeout + mqttConfig.heartbeat_interval }}秒</span>
                                  </div>
                                </div>
                              </div>
                            </div>
                          </div>
                        </el-form>
                      </div>
                      
                      <!-- 操作按钮 -->
                      <div class="config-actions-compact">
                        <el-button type="primary" @click="updateMqttConfig" :loading="configLoading">
                          <i class="el-icon-check"></i>保存配置
                        </el-button>
                      </div>
                    </div>
                  </el-tab-pane>
                  
                  <el-tab-pane label="保持消息" name="retained">
                    <div class="mqtt-section">
                      <div class="section-header">
                        <h4><i class="el-icon-message"></i> 保持消息管理</h4>
                        <div class="header-actions">
                          <button class="header-btn refresh" @click="loadRetainedMessages">
                            <i class="el-icon-refresh"></i>
                            刷新列表
                          </button>
                          <button class="header-btn danger" @click="clearAllRetainedMessages" :disabled="clearRetainedLoading">
                            <i class="el-icon-delete"></i>
                            清空所有
                          </button>
                        </div>
                      </div>
                      <div v-if="retainedMessages && retainedMessages.length > 0">
                          <el-table :data="retainedMessages" border stripe v-loading="retainedLoading" class="standard-table">
                            <el-table-column prop="topic" label="主题" min-width="200" />
                            <el-table-column prop="message" label="消息内容" min-width="250">
                              <template #default="scope">
                                <div class="message-content">
                                  {{ scope.row.message }}
                                </div>
                              </template>
                            </el-table-column>
                            <el-table-column prop="qos" label="QoS等级" width="80" />
                            <el-table-column prop="timestamp" label="保存时间" width="160">
                              <template #default="scope">
                                {{ formatDate(scope.row.timestamp) }}
                              </template>
                            </el-table-column>
                            <el-table-column label="操作" width="100">
                              <template #default="scope">
                                <el-button
                                  size="small"
                                  type="danger"
                                  @click="deleteRetainedMessage(scope.row.topic)"
                                  :loading="deleteRetainedLoading[scope.row.topic]"
                                >
                                  删除
                                </el-button>
                              </template>
                            </el-table-column>
                          </el-table>
                        </div>
                        <p v-else style="text-align: center; color: #999; padding: 20px;">暂无保持消息</p>
                    </div>
                  </el-tab-pane>
                  
                  <el-tab-pane label="主题管理" name="topics">
                    <div class="mqtt-section">
                      <div class="section-header">
                        <h4><i class="el-icon-message"></i> 活跃主题</h4>
                        <el-input
                          v-model="topicFilter"
                          placeholder="搜索主题..."
                          prefix-icon="el-icon-search"
                          clearable
                          class="header-input"
                          style="width: 200px;"
                          size="small"
                        />
                      </div>
                      <div v-if="processedTopics && processedTopics.length > 0">
                        <el-table
                          :data="filteredTopics"
                          class="topic-table"
                          border
                          stripe
                          size="small"
                          :height="'auto'"
                          :max-height="350"
                        >
                            <el-table-column label="主题模式" prop="pattern" min-width="200">
                              <template #default="scope">
                                <div class="topic-pattern">
                                  <i :class="scope.row.type === 'dynamic' ? 'el-icon-folder' : 'el-icon-message'"></i>
                                  <span>{{ scope.row.pattern }}</span>
                                </div>
                              </template>
                            </el-table-column>
                            <el-table-column label="类型" prop="type" width="100" align="center">
                              <template #default="scope">
                                <el-tag :type="scope.row.type === 'dynamic' ? 'warning' : 'success'" size="small">
                                  {{ scope.row.type === 'dynamic' ? '动态主题' : '静态主题' }}
                                </el-tag>
                              </template>
                            </el-table-column>
                            <el-table-column label="活跃数量" prop="count" width="100" align="center">
                              <template #default="scope">
                                <span class="topic-count">{{ scope.row.count }}</span>
                              </template>
                            </el-table-column>

                          </el-table>
                      </div>
                      <p v-else style="text-align: center; color: #999; padding: 20px;">暂无活跃主题</p>
                    </div>
                  </el-tab-pane>
                  
                  <el-tab-pane label="遗嘱管理" name="will">
                    <div class="mqtt-section">
                      <div class="section-header">
                        <h4><i class="el-icon-time"></i> 遗嘱消息管理</h4>
                        <div class="header-actions">
                          <button class="header-btn refresh" @click="loadWillMessages">
                            <i class="el-icon-refresh"></i>
                            刷新列表
                          </button>
                        </div>
                      </div>
                      <div v-if="willMessages && willMessages.length > 0">
                          <el-table :data="willMessages" border stripe v-loading="willLoading" class="standard-table will-messages-table">
                            <el-table-column prop="client_id" label="客户端ID" width="240" class-name="client-id-cell" />
                            <el-table-column prop="topic" label="遗嘱主题" min-width="160" class-name="will-topic-cell" />
                            <el-table-column prop="message" label="遗嘱消息" min-width="180" class-name="will-message-cell">
                              <template #default="scope">
                                <div class="will-message-content">
                                  {{ scope.row.message }}
                                </div>
                              </template>
                            </el-table-column>
                            <el-table-column prop="qos" label="QoS" width="60" class-name="will-compact-cell" />
                            <el-table-column prop="retain" label="保持" width="70" class-name="will-compact-cell">
                              <template #default="scope">
                                <el-tag :type="scope.row.retain ? 'success' : 'info'" size="small">
                                  {{ scope.row.retain ? '是' : '否' }}
                                </el-tag>
                              </template>
                            </el-table-column>
                            <el-table-column prop="is_connected" label="状态" width="70" class-name="will-compact-cell">
                              <template #default="scope">
                                <el-tag :type="scope.row.is_connected ? 'success' : 'danger'" size="small">
                                  {{ scope.row.is_connected ? '在线' : '离线' }}
                                </el-tag>
                              </template>
                            </el-table-column>
                            <el-table-column prop="created_at" label="创建时间" width="150" class-name="will-compact-cell">
                              <template #default="scope">
                                <div class="will-date-cell" :title="formatDate(scope.row.created_at)">
                                  {{ formatCompactDate(scope.row.created_at) }}
                                </div>
                              </template>
                            </el-table-column>
                            <el-table-column label="操作" width="90" class-name="will-action-cell">
                              <template #default="scope">
                                <el-button
                                  size="small"
                                  type="danger"
                                  @click="deleteWillMessage(scope.row.client_id)"
                                  :loading="deleteWillLoading[scope.row.client_id]"
                                  class="will-action-btn"
                                >
                                  清除
                                </el-button>
                              </template>
                            </el-table-column>
                          </el-table>
                        </div>
                        <p v-else style="text-align: center; color: #999; padding: 20px;">暂无遗嘱消息配置</p>
                    </div>
                  </el-tab-pane>
                  
                  <el-tab-pane label="消息发送" name="publish">
                    <div class="publish-grid">
                      <!-- 广播消息卡片 -->
                      <div class="content-card">
                        <div class="card-header">
                          <div>
                            <i class="el-icon-message"></i>
                            广播消息
                          </div>
                        </div>
                        <div class="card-content">
                          <div class="publish-form-compact">
                            <div class="form-row">
                              <label class="form-label">主题</label>
                              <input 
                                v-model="publishForm.topic" 
                                placeholder="请输入主题名称" 
                                class="form-input-compact"
                              />
                            </div>
                            <div class="form-row">
                              <label class="form-label">消息</label>
                              <input 
                                v-model="publishForm.message" 
                                placeholder="请输入消息内容" 
                                class="form-input-compact"
                              />
                            </div>
                            <div class="form-row-inline">
                              <div class="form-col">
                                <label class="form-label">QoS等级</label>
                                <el-select
                                  v-model="publishForm.qos"
                                  class="form-select-modern"
                                  size="small"
                                >
                                  <el-option :value="0" label="0 - 最多一次" />
                                  <el-option :value="1" label="1 - 至少一次" />
                                  <el-option :value="2" label="2 - 仅一次" />
                                </el-select>
                              </div>
                              <div class="form-col">
                                <label class="form-label-checkbox">
                                  <input 
                                    type="checkbox" 
                                    v-model="publishForm.retain" 
                                    class="form-checkbox-compact"
                                  />
                                  保持消息
                                </label>
                              </div>
                            </div>
                            <div class="form-actions">
                              <button 
                                @click="publishMessage" 
                                :disabled="publishLoading"
                                class="publish-btn-compact"
                              >
                                <i class="el-icon-upload"></i>
                                {{ publishLoading ? '发布中...' : '发布消息' }}
                              </button>
                            </div>
                          </div>
                        </div>
                      </div>
                      
                      <!-- 单发消息卡片 -->
                      <div class="content-card">
                        <div class="card-header">
                          <div>
                            <i class="el-icon-share"></i>
                            单发消息
                          </div>
                        </div>
                        <div class="card-content">
                          <div class="publish-form-compact">
                            <div class="form-row">
                              <label class="form-label">目标客户端</label>
                              <el-select
                                v-model="sendForm.clientId"
                                @change="onClientChange"
                                placeholder="选择客户端"
                                class="form-select-modern"
                                filterable
                                clearable
                                size="small"
                              >
                                <template #prefix>
                                  <span class="select-prefix">{{ mqttStats.client_list?.length || 0 }}个在线</span>
                                </template>
                                <el-option
                                  v-for="client in mqttStats.client_list"
                                  :key="client.id"
                                  :value="client.id"
                                  :label="`${client.id} ${client.username ? `[${client.username}]` : '[未认证]'}`"
                                >
                                  <div class="client-option">
                                    <div class="client-info">
                                      <span class="client-id">{{ client.id }}</span>
                                      <span class="client-username">{{ client.username ? `[${client.username}]` : '[未认证]' }}</span>
                                    </div>
                                    <span class="client-topics">{{ client.subscribed_topics?.length || 0 }}个主题</span>
                                  </div>
                                </el-option>
                              </el-select>
                            </div>
                            <div class="form-row">
                              <label class="form-label">订阅主题</label>
                              <el-select
                                v-model="sendForm.topic"
                                :placeholder="selectedClientTopics.length ? '选择主题' : '请先选择客户端'"
                                class="form-select-modern"
                                :disabled="!selectedClientTopics.length"
                                filterable
                                clearable
                                size="small"
                              >
                                <el-option
                                  v-for="topic in selectedClientTopics"
                                  :key="topic"
                                  :value="topic"
                                  :label="topic"
                                />
                              </el-select>
                              <div v-if="sendForm.clientId && !selectedClientTopics.length" class="form-hint">
                                该客户端暂未订阅任何主题
                              </div>
                            </div>
                            <div class="form-row">
                              <label class="form-label">消息</label>
                              <input 
                                v-model="sendForm.message" 
                                placeholder="请输入消息内容" 
                                class="form-input-compact"
                              />
                            </div>
                            <div class="form-row-inline">
                              <div class="form-col">
                                <label class="form-label">QoS等级</label>
                                <el-select
                                  v-model="sendForm.qos"
                                  class="form-select-modern"
                                  size="small"
                                >
                                  <el-option :value="0" label="0 - 最多一次" />
                                  <el-option :value="1" label="1 - 至少一次" />
                                  <el-option :value="2" label="2 - 仅一次" />
                                </el-select>
                              </div>
                              <div class="form-col">
                                <label class="form-label-checkbox">
                                  <input 
                                    type="checkbox" 
                                    v-model="sendForm.retain" 
                                    class="form-checkbox-compact"
                                  />
                                  保持消息
                                </label>
                              </div>
                            </div>
                            <div class="form-actions">
                              <button 
                                @click="sendToClient" 
                                :disabled="sendLoading"
                                class="send-btn-compact"
                              >
                                <i class="el-icon-position"></i>
                                {{ sendLoading ? '发送中...' : '发送消息' }}
                              </button>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </el-tab-pane>
                </el-tabs>
            </div>

            <!-- 操作日志页面 -->
            <div v-else-if="activeMenu === 'logs'" class="logs-management">
              <!-- 操作工具栏 -->
              <div class="modern-card" style="margin-bottom: 1rem;">
                <div class="card-header">
                  <h3 class="card-title">
                    <i class="fas fa-clipboard-list"></i>
                    日志管理
                  </h3>
                  <div style="display: flex; gap: 1rem;">
                    <button @click="refreshLogs" class="modern-btn secondary">
                      <i class="fas fa-sync-alt"></i>
                      刷新
                    </button>
                    <button @click="clearLogs" class="modern-btn danger">
                      <i class="fas fa-trash"></i>
                      清理日志
                    </button>
                  </div>
                </div>
              </div>
              
              <!-- 日志统计 -->
              <div class="stats-grid">
                <div class="stat-card">
                  <div class="stat-header">
                    <span class="stat-label">今日操作</span>
                    <div class="stat-icon primary">
                      <i class="fas fa-calendar-day"></i>
                    </div>
                  </div>
                  <div class="stat-value">{{ logStats.today_count || 0 }}</div>
                </div>

                <div class="stat-card">
                  <div class="stat-header">
                    <span class="stat-label">成功率</span>
                    <div class="stat-icon success">
                      <i class="fas fa-check-circle"></i>
                    </div>
                  </div>
                  <div class="stat-value">{{ (logStats.success_rate || 0).toFixed(1) }}%</div>
                </div>

                <div class="stat-card">
                  <div class="stat-header">
                    <span class="stat-label">快捷清理</span>
                    <div class="stat-icon warning">
                      <i class="fas fa-broom"></i>
                    </div>
                  </div>
                  <div style="display: flex; gap: 0.5rem; margin-top: 1rem;">
                    <button @click="quickClearLogs(1)" class="modern-btn danger small" title="清理1天前的日志（几乎全部）">
                      1天前
                    </button>
                    <button @click="quickClearLogs(7)" class="modern-btn secondary small" title="清理7天前的日志">
                      7天前
                    </button>
                    <button @click="quickClearLogs(30)" class="modern-btn secondary small" title="清理30天前的日志">
                      30天前
                    </button>
                  </div>
                </div>
              </div>
              
              <!-- 过滤器 -->
              <div class="logs-filters">
                <div class="filter-row">
                  <div class="filter-item">
                    <label>操作类型:</label>
                    <select v-model="logFilters.operation_type" class="filter-select">
                      <option value="">全部</option>
                      <option value="LOGIN">登录</option>
                      <option value="LOGIN_FAILED">登录失败</option>
                      <option value="CREATE_USER">创建用户</option>
                      <option value="UPDATE_USER">更新用户</option>
                      <option value="DELETE_USER">删除用户</option>
                    </select>
                  </div>
                  <div class="filter-item">
                    <label>模块:</label>
                    <select v-model="logFilters.module" class="filter-select">
                      <option value="">全部</option>
                      <option value="AUTH">认证</option>
                      <option value="USER_MANAGEMENT">用户管理</option>
                      <option value="MQTT">MQTT服务器</option>
                      <option value="SYSTEM">系统</option>
                    </select>
                  </div>
                  <div class="filter-item">
                    <label>搜索:</label>
                    <input 
                      v-model="logFilters.keyword" 
                      placeholder="搜索操作详情..." 
                      class="filter-input"
                    />
                  </div>
                  <div class="filter-item">
                    <button @click="filterLogs" class="filter-btn">
                      <i class="el-icon-search"></i>
                      搜索
                    </button>
                  </div>
                </div>
              </div>
              
              <!-- 日志表格 -->
              <div class="logs-table">
                <div class="table-header">
                  <div class="table-cell">时间</div>
                  <div class="table-cell">用户</div>
                  <div class="table-cell">操作类型</div>
                  <div class="table-cell">模块</div>
                  <div class="table-cell">操作详情</div>
                  <div class="table-cell">结果</div>
                  <div class="table-cell">IP地址</div>
                </div>
                <div class="table-body">
                  <div v-if="logs.length === 0" class="no-logs">
                    <i class="el-icon-document"></i>
                    <p>暂无操作日志</p>
                  </div>
                  <div v-else>
                    <div v-for="log in logs" :key="log.id" class="table-row">
                      <div class="table-cell">{{ formatTime(log.created_at) }}</div>
                      <div class="table-cell">{{ log.username }}</div>
                      <div class="table-cell">
                        <span :class="getOperationTypeClass(log.operation_type)">
                          {{ formatOperationType(log.operation_type) }}
                        </span>
                      </div>
                      <div class="table-cell">{{ log.module }}</div>
                      <div class="table-cell">{{ log.operation_detail }}</div>
                      <div class="table-cell">
                        <span :class="log.success ? 'success' : 'failed'">
                          {{ log.success ? '成功' : '失败' }}
                        </span>
                      </div>
                      <div class="table-cell">{{ formatIpAddress(log.ip_address) }}</div>
                    </div>
                  </div>
                </div>
              </div>
              
              <!-- 分页组件 -->
              <div class="logs-pagination">
                <el-pagination
                  v-model:current-page="logPagination.page"
                  v-model:page-size="logPagination.page_size"
                  :page-sizes="[10, 20, 50, 100]"
                  :total="logPagination.total_count"
                  layout="total, sizes, prev, pager, next, jumper"
                  @size-change="handleLogPageSizeChange"
                  @current-change="handleLogPageChange"
                  :small="false"
                  background
                  :hide-on-single-page="false"
                />
              </div>
            </div>

            <!-- 系统设置页面 -->
            <div v-else-if="activeMenu === 'settings'" class="settings-management">
              <!-- 设置标签页 -->
              <el-tabs v-model="activeSettingsTab" class="settings-tabs">
                <!-- CORS设置 -->
                <el-tab-pane label="CORS设置" name="cors">
                  <div class="modern-card">
                    <div class="card-header">
                      <h3 class="card-title">
                        <i class="fas fa-globe"></i>
                        跨域资源共享(CORS)配置
                      </h3>
                      <div class="card-actions">
                        <button @click="loadSecurityConfig('security')" class="modern-btn secondary">
                          <i class="fas fa-sync-alt"></i>
                          刷新
                        </button>
                        <button @click="saveSecurityConfig" class="modern-btn primary" :disabled="configSaving">
                          <i class="fas fa-save"></i>
                          {{ configSaving ? '保存中...' : '保存配置' }}
                        </button>
                      </div>
                    </div>
                    <div class="card-content">
                      <el-form :model="securityConfig" label-width="150px" class="security-form">
                        <el-form-item label="开发模式">
                          <el-switch
                            v-model="securityConfig.cors_dev_mode"
                            active-text="启用"
                            inactive-text="禁用"
                            active-color="#f56c6c"
                            inactive-color="#13ce66"
                          />
                          <div class="form-help" style="color: #f56c6c;">
                            <i class="fas fa-exclamation-triangle"></i>
                            开发模式将允许所有来源访问API，仅用于开发环境，生产环境请务必禁用！
                          </div>
                        </el-form-item>
                        <el-form-item label="允许的源地址" v-show="!securityConfig.cors_dev_mode">
                          <div style="display: flex; flex-direction: column; gap: 10px;">
                            <el-input
                              v-model="securityConfig.cors_allowed_origins"
                              type="textarea"
                              :rows="3"
                              placeholder="多个地址用逗号分隔，例如：http://localhost:5173,http://***********:5173,https://yourdomain.com"
                            />
                            <div style="display: flex; gap: 10px; flex-wrap: wrap;">
                              <el-button size="small" type="primary" @click="addCurrentOrigin">
                                <i class="fas fa-plus"></i>
                                添加当前访问地址
                              </el-button>
                              <el-button size="small" type="success" @click="addCommonOrigins">
                                <i class="fas fa-magic"></i>
                                添加常用地址
                              </el-button>
                              <el-button size="small" type="warning" @click="resetToDefaults">
                                <i class="fas fa-undo"></i>
                                重置为默认
                              </el-button>
                            </div>
                          </div>
                          <div class="form-help">
                            配置允许访问API的源地址，生产环境请勿使用 * 通配符
                          </div>
                        </el-form-item>
                        <el-form-item label="允许的HTTP方法">
                          <el-input
                            v-model="securityConfig.cors_allowed_methods"
                            placeholder="例如：GET,POST,PUT,DELETE,OPTIONS"
                          />
                          <div class="form-help">
                            配置允许的HTTP请求方法，用逗号分隔
                          </div>
                        </el-form-item>
                        <el-form-item label="允许的请求头">
                          <el-input
                            v-model="securityConfig.cors_allowed_headers"
                            placeholder="例如：Origin,Content-Type,Authorization"
                          />
                          <div class="form-help">
                            配置允许的请求头，用逗号分隔
                          </div>
                        </el-form-item>
                        <el-form-item label="允许携带凭证">
                          <el-switch
                            v-model="securityConfig.cors_allow_credentials"
                            active-text="允许"
                            inactive-text="禁止"
                          />
                          <div class="form-help">
                            是否允许请求携带Cookie等凭证信息
                          </div>
                        </el-form-item>
                        <el-form-item label="预检缓存时间">
                          <div class="input-container">
                            <div>
                              <el-input-number
                                v-model="securityConfig.cors_max_age"
                                :min="0"
                                :max="604800"
                                :step="3600"
                                style="width: 200px"
                              />
                              <span style="margin-left: 10px; color: #999;">秒</span>
                            </div>
                            <div class="config-help">
                              <span class="range">建议值: 86400 (24小时)</span>
                              <div class="desc">预检请求的缓存时间</div>
                            </div>
                          </div>
                        </el-form-item>
                      </el-form>
                    </div>
                  </div>
                </el-tab-pane>

                <!-- 频率限制设置 -->
                <el-tab-pane label="频率限制" name="rate-limit">
                  <div class="modern-card">
                    <div class="card-header">
                      <h3 class="card-title">
                        <i class="fas fa-tachometer-alt"></i>
                        API频率限制配置
                      </h3>
                      <div class="card-actions">
                        <button @click="loadSecurityConfig('rate_limit')" class="modern-btn secondary">
                          <i class="fas fa-sync-alt"></i>
                          刷新
                        </button>
                        <button @click="saveSecurityConfig" class="modern-btn primary" :disabled="configSaving">
                          <i class="fas fa-save"></i>
                          {{ configSaving ? '保存中...' : '保存配置' }}
                        </button>
                      </div>
                    </div>
                    <div class="card-content">
                      <el-form :model="rateLimitConfig" label-width="150px" class="security-form">
                        <el-form-item label="启用频率限制">
                          <el-switch
                            v-model="rateLimitConfig.enable_rate_limit"
                            active-text="启用"
                            inactive-text="禁用"
                          />
                          <div class="form-help">
                            启用后将对API请求进行频率限制，防止滥用
                          </div>
                        </el-form-item>
                        <el-form-item label="每分钟请求数">
                          <div class="input-container">
                            <div>
                              <el-input-number
                                v-model="rateLimitConfig.requests_per_minute"
                                :min="10"
                                :max="1000"
                                :step="10"
                                style="width: 200px"
                                :disabled="!rateLimitConfig.enable_rate_limit"
                              />
                              <span style="margin-left: 10px; color: #999;">次/分钟</span>
                            </div>
                            <div class="config-help">
                              <span class="range">安全范围: 10-1000</span>
                              <div class="desc">每个IP每分钟允许的最大请求数</div>
                            </div>
                          </div>
                        </el-form-item>
                        <el-form-item label="突发请求缓冲">
                          <div class="input-container">
                            <div>
                              <el-input-number
                                v-model="rateLimitConfig.burst_size"
                                :min="5"
                                :max="200"
                                :step="5"
                                style="width: 200px"
                                :disabled="!rateLimitConfig.enable_rate_limit"
                              />
                              <span style="margin-left: 10px; color: #999;">次</span>
                            </div>
                            <div class="config-help">
                              <span class="range">安全范围: 5-200</span>
                              <div class="desc">允许的瞬间突发请求数量</div>
                            </div>
                          </div>
                        </el-form-item>
                        <el-form-item label="清理间隔">
                          <div class="input-container">
                            <div>
                              <el-input-number
                                v-model="rateLimitConfig.cleanup_interval"
                                :min="60"
                                :max="3600"
                                :step="60"
                                style="width: 200px"
                                :disabled="!rateLimitConfig.enable_rate_limit"
                              />
                              <span style="margin-left: 10px; color: #999;">秒</span>
                            </div>
                            <div class="config-help">
                              <span class="range">安全范围: 60-3600</span>
                              <div class="desc">清理过期访问记录的时间间隔</div>
                            </div>
                          </div>
                        </el-form-item>
                      </el-form>
                    </div>
                  </div>
                </el-tab-pane>

                <!-- 数据库配置 -->
                <el-tab-pane label="数据库配置" name="database">
                  <div class="modern-card">
                    <div class="card-header">
                      <h3 class="card-title">
                        <i class="fas fa-database"></i>
                        数据库连接池配置
                      </h3>
                      <div class="card-actions">
                        <button @click="loadSecurityConfig('database')" class="modern-btn secondary">
                          <i class="fas fa-sync-alt"></i>
                          刷新
                        </button>
                        <button @click="saveSecurityConfig" class="modern-btn primary" :disabled="configSaving">
                          <i class="fas fa-save"></i>
                          {{ configSaving ? '保存中...' : '保存配置' }}
                        </button>
                        <button @click="reloadConfig" class="modern-btn warning" :disabled="configSaving">
                          <i class="fas fa-redo"></i>
                          重载配置
                        </button>
                      </div>
                    </div>
                    <div class="card-content">
                      <el-form :model="databaseConfig" label-width="150px" class="security-form">
                        <el-form-item label="最大连接数">
                          <div class="input-container">
                            <div>
                              <el-input-number
                                v-model="databaseConfig.max_open_conns"
                                :min="5"
                                :max="200"
                                :step="5"
                                style="width: 200px"
                              />
                              <span style="margin-left: 10px; color: #999;">个</span>
                            </div>
                            <div class="config-help">
                              <span class="range">安全范围: 5-200</span>
                              <div class="desc">数据库连接池的最大连接数</div>
                            </div>
                          </div>
                        </el-form-item>
                        <el-form-item label="最大空闲连接">
                          <div class="input-container">
                            <div>
                              <el-input-number
                                v-model="databaseConfig.max_idle_conns"
                                :min="1"
                                :max="50"
                                :step="1"
                                style="width: 200px"
                              />
                              <span style="margin-left: 10px; color: #999;">个</span>
                            </div>
                            <div class="config-help">
                              <span class="range">安全范围: 1-50</span>
                              <div class="desc">连接池中保持的最大空闲连接数</div>
                            </div>
                          </div>
                        </el-form-item>
                        <el-form-item label="连接最大生存时间">
                          <div class="input-container">
                            <div>
                              <el-input-number
                                v-model="databaseConfig.conn_max_lifetime"
                                :min="60"
                                :max="7200"
                                :step="60"
                                style="width: 200px"
                              />
                              <span style="margin-left: 10px; color: #999;">秒</span>
                            </div>
                            <div class="config-help">
                              <span class="range">安全范围: 60-7200</span>
                              <div class="desc">连接的最大生存时间</div>
                            </div>
                          </div>
                        </el-form-item>
                        <el-form-item label="连接最大空闲时间">
                          <div class="input-container">
                            <div>
                              <el-input-number
                                v-model="databaseConfig.conn_max_idle_time"
                                :min="30"
                                :max="1800"
                                :step="30"
                                style="width: 200px"
                              />
                              <span style="margin-left: 10px; color: #999;">秒</span>
                            </div>
                            <div class="config-help">
                              <span class="range">安全范围: 30-1800</span>
                              <div class="desc">连接在池中的最大空闲时间</div>
                            </div>
                          </div>
                        </el-form-item>
                      </el-form>
                    </div>
                  </div>
                </el-tab-pane>

                <!-- 输入验证配置 -->
                <el-tab-pane label="输入验证" name="validation">
                  <div class="modern-card">
                    <div class="card-header">
                      <h3 class="card-title">
                        <i class="fas fa-shield-alt"></i>
                        输入验证和安全配置
                      </h3>
                      <div class="card-actions">
                        <button @click="loadSecurityConfig('validation')" class="modern-btn secondary">
                          <i class="fas fa-sync-alt"></i>
                          刷新
                        </button>
                        <button @click="saveSecurityConfig" class="modern-btn primary" :disabled="configSaving">
                          <i class="fas fa-save"></i>
                          {{ configSaving ? '保存中...' : '保存配置' }}
                        </button>
                      </div>
                    </div>
                    <div class="card-content">
                      <el-form :model="validationConfig" label-width="150px" class="security-form">
                        <el-form-item label="用户名最大长度">
                          <el-input-number
                            v-model="validationConfig.max_username_length"
                            :min="1"
                            :max="100"
                            :step="1"
                            style="width: 200px"
                          />
                          <span style="margin-left: 10px; color: var(--text-secondary);">字符</span>
                        </el-form-item>
                        <el-form-item label="密码最大长度">
                          <el-input-number
                            v-model="validationConfig.max_password_length"
                            :min="8"
                            :max="256"
                            :step="8"
                            style="width: 200px"
                          />
                          <span style="margin-left: 10px; color: var(--text-secondary);">字符</span>
                        </el-form-item>
                        <el-form-item label="手机号最大长度">
                          <el-input-number
                            v-model="validationConfig.max_phone_length"
                            :min="10"
                            :max="30"
                            :step="1"
                            style="width: 200px"
                          />
                          <span style="margin-left: 10px; color: var(--text-secondary);">字符</span>
                        </el-form-item>
                        <el-form-item label="备注最大长度">
                          <el-input-number
                            v-model="validationConfig.max_remark_length"
                            :min="50"
                            :max="500"
                            :step="50"
                            style="width: 200px"
                          />
                          <span style="margin-left: 10px; color: var(--text-secondary);">字符</span>
                        </el-form-item>
                        <el-form-item label="XSS防护">
                          <el-switch
                            v-model="validationConfig.enable_xss_protection"
                            active-text="启用"
                            inactive-text="禁用"
                          />
                          <div class="form-help">
                            启用XSS(跨站脚本)攻击防护，检测和过滤恶意脚本
                          </div>
                        </el-form-item>
                      </el-form>
                    </div>
                  </div>
                </el-tab-pane>

                <!-- 开发调试设置 -->
                <el-tab-pane label="开发调试" name="debug">
                  <div class="modern-card">
                    <div class="card-header">
                      <h3 class="card-title">
                        <i class="fas fa-bug"></i>
                        开发调试设置
                      </h3>
                      <div class="card-actions">
                        <button @click="loadDebugConfig" class="modern-btn secondary">
                          <i class="fas fa-sync-alt"></i>
                          刷新
                        </button>
                        <button @click="saveDebugConfig" class="modern-btn primary" :disabled="configSaving">
                          <i class="fas fa-save"></i>
                          {{ configSaving ? '保存中...' : '保存配置' }}
                        </button>
                      </div>
                    </div>
                    <div class="card-content">
                      <el-form :model="debugConfig" label-width="150px" class="security-form">
                        <el-form-item label="调试模式">
                          <el-switch
                            v-model="debugConfig.debug_mode"
                            active-text="启用"
                            inactive-text="禁用"
                          />
                          <div class="form-help">启用后将禁用前端缓存，便于开发调试。生产环境请关闭。</div>
                        </el-form-item>

                        <el-form-item label="前端调试日志">
                          <el-switch
                            v-model="debugConfig.frontend_debug_logs"
                            active-text="启用"
                            inactive-text="禁用"
                          />
                          <div class="form-help">启用前端控制台调试日志输出</div>
                        </el-form-item>
                      </el-form>
                    </div>
                  </div>
                </el-tab-pane>

                <!-- 验证码设置 -->
                <el-tab-pane label="验证码设置" name="captcha">
                  <div class="modern-card">
                    <div class="card-header">
                      <h3 class="card-title">
                        <i class="fas fa-puzzle-piece"></i>
                        验证码配置和调试设置
                      </h3>
                      <div class="card-actions">
                        <button @click="loadCaptchaConfig" class="modern-btn secondary">
                          <i class="fas fa-sync-alt"></i>
                          刷新
                        </button>
                        <button @click="saveCaptchaConfig" class="modern-btn primary" :disabled="configSaving">
                          <i class="fas fa-save"></i>
                          {{ configSaving ? '保存中...' : '保存配置' }}
                        </button>
                      </div>
                    </div>
                    <div class="card-content">
                      <el-form :model="captchaConfig" label-width="150px" class="security-form">
                        <el-form-item label="图片宽度">
                          <el-input-number
                            v-model="captchaConfig.img_width"
                            :min="200"
                            :max="600"
                            :step="50"
                            style="width: 200px"
                          />
                          <span style="margin-left: 10px; color: var(--text-secondary);">像素</span>
                          <div class="form-help">
                            验证码图片的宽度，建议300-400像素
                          </div>
                        </el-form-item>
                        <el-form-item label="图片高度">
                          <el-input-number
                            v-model="captchaConfig.img_height"
                            :min="100"
                            :max="300"
                            :step="25"
                            style="width: 200px"
                          />
                          <span style="margin-left: 10px; color: var(--text-secondary);">像素</span>
                          <div class="form-help">
                            验证码图片的高度，建议150-200像素
                          </div>
                        </el-form-item>
                        <el-form-item label="红圈半径">
                          <el-input-number
                            v-model="captchaConfig.point_radius"
                            :min="10"
                            :max="50"
                            :step="5"
                            style="width: 200px"
                          />
                          <span style="margin-left: 10px; color: var(--text-secondary);">像素</span>
                          <div class="form-help">
                            验证码红色圆圈的半径大小
                          </div>
                        </el-form-item>
                        <el-form-item label="容错半径">
                          <el-input-number
                            v-model="captchaConfig.tolerance_radius"
                            :min="10"
                            :max="50"
                            :step="5"
                            style="width: 200px"
                          />
                          <span style="margin-left: 10px; color: var(--text-secondary);">像素</span>
                          <div class="form-help">
                            点击验证的容错范围，数值越小验证越严格
                          </div>
                        </el-form-item>
                        <el-form-item label="最小距离">
                          <el-input-number
                            v-model="captchaConfig.min_distance"
                            :min="50"
                            :max="150"
                            :step="10"
                            style="width: 200px"
                          />
                          <span style="margin-left: 10px; color: var(--text-secondary);">像素</span>
                          <div class="form-help">
                            验证码圆圈之间的最小距离
                          </div>
                        </el-form-item>
                        <el-form-item label="验证码点数">
                          <el-input-number
                            v-model="captchaConfig.num_points"
                            :min="2"
                            :max="6"
                            :step="1"
                            style="width: 200px"
                          />
                          <span style="margin-left: 10px; color: var(--text-secondary);">个</span>
                          <div class="form-help">
                            需要点击的红色圆圈数量
                          </div>
                        </el-form-item>
                        <el-form-item label="过期时间">
                          <el-input-number
                            v-model="captchaConfig.expire_minutes"
                            :min="1"
                            :max="10"
                            :step="1"
                            style="width: 200px"
                          />
                          <span style="margin-left: 10px; color: var(--text-secondary);">分钟</span>
                          <div class="form-help">
                            验证码的有效期，超时需要重新获取
                          </div>
                        </el-form-item>
                        <el-form-item label="调试输出">
                          <el-switch
                            v-model="captchaConfig.debug_output"
                            active-text="启用"
                            inactive-text="禁用"
                            active-color="#f56c6c"
                            inactive-color="#13ce66"
                          />
                          <div class="form-help" :style="{ color: captchaConfig.debug_output ? '#f56c6c' : 'var(--text-secondary)' }">
                            <i class="fas fa-exclamation-triangle" v-if="captchaConfig.debug_output"></i>
                            <i class="fas fa-info-circle" v-else></i>
                            {{ captchaConfig.debug_output ? '启用后会在服务器控制台输出验证码坐标等调试信息，生产环境建议禁用以防信息泄露' : '禁用验证码调试信息输出，提高安全性' }}
                          </div>
                        </el-form-item>
                      </el-form>
                    </div>
                  </div>
                </el-tab-pane>

                <!-- RSA证书管理 -->
                <el-tab-pane label="RSA证书管理" name="certificates">
                  <div class="modern-card">
                    <div class="card-header">
                      <h3 class="card-title">
                        <i class="fas fa-key"></i>
                        RSA证书管理
                      </h3>
                      <div class="card-actions">
                        <button @click="loadRSACertificateInfo" class="modern-btn secondary">
                          <i class="fas fa-sync-alt"></i>
                          刷新
                        </button>
                        <button @click="showRSACertDialog = true" class="modern-btn primary">
                          <i class="fas fa-plus"></i>
                          生成新证书
                        </button>
                      </div>
                    </div>
                    <div class="card-content">
                      <!-- RSA密钥对状态 -->
                      <div class="rsa-status-section" v-loading="rsaCertLoading">


                        <el-descriptions :column="2" border style="margin-bottom: 20px;">
                          <el-descriptions-item label="RSA密钥对状态">
                            <el-tag :type="rsaCertInfo.key_pair_initialized ? 'success' : 'danger'">
                              {{ rsaCertInfo.key_pair_initialized ? '已初始化' : '未初始化' }}
                            </el-tag>
                          </el-descriptions-item>
                          <el-descriptions-item label="当前密钥长度">
                            <el-tag type="primary">{{ rsaCertInfo.key_info?.key_size || 2048 }}位</el-tag>
                          </el-descriptions-item>
                          <el-descriptions-item label="私钥文件">
                            <span :style="{ color: rsaCertInfo.key_info?.private_key_exists ? '#67c23a' : '#f56c6c' }">
                              <i :class="rsaCertInfo.key_info?.private_key_exists ? 'fas fa-check-circle' : 'fas fa-times-circle'"></i>
                              {{ rsaCertInfo.key_info?.private_key_exists ? '存在' : '不存在' }}
                            </span>
                          </el-descriptions-item>
                          <el-descriptions-item label="公钥文件">
                            <span :style="{ color: rsaCertInfo.key_info?.public_key_exists ? '#67c23a' : '#f56c6c' }">
                              <i :class="rsaCertInfo.key_info?.public_key_exists ? 'fas fa-check-circle' : 'fas fa-times-circle'"></i>
                              {{ rsaCertInfo.key_info?.public_key_exists ? '存在' : '不存在' }}
                            </span>
                          </el-descriptions-item>
                          <el-descriptions-item label="生成的证书数量">
                            <el-tag type="info">{{ rsaCertInfo.total_rsa_certs || 0 }} 个</el-tag>
                          </el-descriptions-item>
                          <el-descriptions-item label="密钥来源">
                            <el-tag type="success">文件存储</el-tag>
                          </el-descriptions-item>
                        </el-descriptions>

                        <!-- 说明信息 -->
                        <el-alert
                          title="RSA证书说明"
                          type="info"
                          :closable="false"
                          style="margin-top: 15px;"
                        >
                          <template #default>
                            <p><strong>当前使用：</strong>{{ getCurrentCertificateDescription() }}</p>
                            <p><strong>默认证书：</strong>system_default.crt 是系统默认证书（永久有效），用于调试和开发。</p>
                            <p><strong>生成的证书：</strong>每个生成的证书都有自己的密钥对，可以分发给客户端使用。</p>
                            <p><strong>证书切换：</strong>您可以将任何证书切换为系统使用的证书，切换后立即生效。</p>
                            <p><strong>重要提醒：</strong>每个证书只能用对应的私钥解密，不同证书的密钥对是独立的。</p>
                          </template>
                        </el-alert>
                      </div>

                      <!-- RSA证书列表 -->
                      <div class="rsa-certificate-list">
                        <h4 style="margin: 20px 0 10px 0;">
                          <i class="fas fa-list"></i>
                          RSA证书列表
                        </h4>
                        <el-table :data="rsaCertificateList" style="width: 100%" v-loading="certificateLoading">
                          <el-table-column prop="name" label="证书名称" width="250">
                            <template #default="scope">
                              <div class="certificate-name">
                                <i :class="scope.row.is_default_cert ? 'fas fa-shield-alt' : 'fas fa-certificate'"
                                   style="margin-right: 8px;"
                                   :style="{ color: scope.row.is_default_cert ? '#67c23a' : '#409eff' }"></i>
                                {{ scope.row.name }}
                                <el-tag v-if="scope.row.is_default_cert" type="success" size="small" style="margin-left: 8px;">
                                  默认
                                </el-tag>
                              </div>
                            </template>
                          </el-table-column>
                          <el-table-column prop="subject" label="证书主题" min-width="200" show-overflow-tooltip />
                          <el-table-column prop="not_after" label="过期时间" width="120">
                            <template #default="scope">
                              <span :style="{ color: scope.row.is_expired ? '#f56c6c' : (scope.row.days_to_expiry <= 30 ? '#e6a23c' : '#67c23a') }">
                                {{ formatDate(scope.row.not_after) }}
                              </span>
                            </template>
                          </el-table-column>
                          <el-table-column prop="days_to_expiry" label="剩余天数" width="100">
                            <template #default="scope">
                              <span :style="{ color: scope.row.is_expired ? '#f56c6c' : (scope.row.days_to_expiry <= 30 ? '#e6a23c' : '#67c23a') }">
                                {{ scope.row.is_expired ? '已过期' : scope.row.days_to_expiry + '天' }}
                              </span>
                            </template>
                          </el-table-column>
                          <el-table-column prop="key_size" label="密钥长度" width="100">
                            <template #default="scope">
                              <el-tag size="small">{{ scope.row.key_size || 2048 }}位</el-tag>
                            </template>
                          </el-table-column>
                          <el-table-column label="当前使用" width="80">
                            <template #default="scope">
                              <el-tag v-if="scope.row.is_currently_used" type="success">使用中</el-tag>
                              <el-tag v-else type="info">未使用</el-tag>
                            </template>
                          </el-table-column>

                          <el-table-column label="操作" width="200">
                            <template #default="scope">
                              <div class="button-group">
                                <el-tooltip content="下载证书" placement="top">
                                  <el-button size="small" type="primary" circle @click="downloadCertificate(scope.row)">
                                    <i class="fas fa-download"></i>
                                  </el-button>
                                </el-tooltip>
                                <el-tooltip
                                  :content="getSwitchButtonTooltip(scope.row)"
                                  placement="top"
                                >
                                  <el-button
                                    size="small"
                                    type="success"
                                    circle
                                    @click="switchToCertificate(scope.row)"
                                    :disabled="scope.row.is_currently_used"
                                  >
                                    <i class="fas fa-exchange-alt"></i>
                                  </el-button>
                                </el-tooltip>
                                <el-tooltip content="删除证书" placement="top">
                                  <el-button
                                    size="small"
                                    type="danger"
                                    circle
                                    @click="deleteCertificate(scope.row)"
                                    :disabled="scope.row.is_currently_used || scope.row.is_default_cert"
                                  >
                                    <i class="fas fa-trash"></i>
                                  </el-button>
                                </el-tooltip>
                              </div>
                            </template>
                          </el-table-column>
                        </el-table>

                        <!-- 空状态 -->
                        <div v-if="rsaCertificateList.length === 0" class="empty-state">
                          <el-empty description="暂无RSA证书">
                            <el-button type="primary" @click="showRSACertDialog = true">
                              <i class="fas fa-plus"></i>
                              生成第一个证书
                            </el-button>
                          </el-empty>
                        </div>
                      </div>
                    </div>
                  </div>
                </el-tab-pane>


              </el-tabs>
            </div>

            <!-- 其他页面占位符 -->
            <div v-else class="placeholder-content">
              <div class="placeholder-icon">
                <i class="el-icon-info"></i>
              </div>
              <h3>功能开发中</h3>
              <p>{{ getMenuTitle(activeMenu) }} 功能正在开发中，敬请期待！</p>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 用户管理对话框 -->
    <el-dialog
      v-model="userDialogVisible"
      :title="isEditMode ? '编辑用户' : '添加用户'"
      width="800px"
      class="modern-dialog"
      :close-on-click-modal="false"
      top="5vh"
      :style="{ maxHeight: '80vh' }"
    >
      <div class="modern-form-container">
        <el-form
          :key="userFormKey"
          :model="userForm"
          :rules="userFormRules"
          ref="userFormRef"
          label-width="100px"
          class="modern-form"
        >
          <!-- 基本信息区块 -->
          <div class="form-section">
            <div class="section-header">
              <h4 class="section-title">
                <i class="fas fa-user"></i>
                基本信息
              </h4>
            </div>
            <div class="section-content">
              <el-row :gutter="16">
                <el-col :span="12">
                  <el-form-item label="用户名" prop="username">
                    <el-input
                      v-model="userForm.username"
                      placeholder="请输入用户名"
                      :disabled="isEditMode || userForm.username === 'admin'"
                      autocomplete="new-username"
                      :key="`username-${userFormKey}`"
                    />
                    <div v-if="userForm.username === 'admin'" class="form-help">
                      admin超级管理员账户用户名不可修改
                    </div>
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="密码" prop="password" :required="!isEditMode">
                    <el-input
                      v-model="userForm.password"
                      type="password"
                      :placeholder="isEditMode ? '留空则不修改密码' : '请输入密码'"
                      show-password
                      autocomplete="new-password"
                      :key="`password-${userFormKey}`"
                    />
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="账户状态" prop="is_active">
                    <el-switch
                      v-model="userForm.is_active"
                      active-text="启用"
                      inactive-text="禁用"
                      :disabled="userForm.username === 'admin'"
                    />
                  </el-form-item>
                </el-col>
                <el-col :span="24">
                  <el-form-item label="备注">
                    <el-input
                      v-model="userForm.remark"
                      type="textarea"
                      placeholder="请输入备注信息"
                      :rows="2"
                    />
                  </el-form-item>
                </el-col>
              </el-row>
            </div>
          </div>
          
          
          <!-- 权限管理区块 -->
          <div class="form-section">
            <div class="section-header">
              <h4 class="section-title">
                <i class="fas fa-shield-alt"></i>
                权限管理
              </h4>
            </div>
            <div class="section-content">
              <el-row :gutter="16">
                <el-col :span="12">
                  <el-form-item label="后台权限">
                    <el-switch
                      v-model="userForm.can_login_backend"
                      active-text="允许"
                      inactive-text="禁止"
                      :disabled="userForm.username === 'admin'"
                    />
                    <div class="form-help">
                      {{ userForm.username === 'admin' ? 'admin超级管理员账户默认拥有所有权限' : '是否允许登录后台管理系统' }}
                    </div>
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="MQTT权限">
                    <el-switch
                      v-model="userForm.can_login_mqtt"
                      active-text="允许"
                      inactive-text="禁止"
                      :disabled="userForm.username === 'admin'"
                    />
                    <div class="form-help">
                      {{ userForm.username === 'admin' ? 'admin超级管理员账户默认拥有所有权限' : '是否允许连接MQTT服务器' }}
                    </div>
                  </el-form-item>
                </el-col>
              </el-row>
            </div>
          </div>

        </el-form>
      </div>
      
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="userDialogVisible = false" size="default">
            <i class="fas fa-times"></i>
            取消
          </el-button>
          <el-button type="primary" @click="saveUser" :loading="saveLoading" size="default">
            <i class="fas fa-save"></i>
            {{ isEditMode ? '更新' : '创建' }}
          </el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 黑名单管理对话框 -->
    <el-dialog
      v-model="blacklistDialogVisible"
      title="添加黑名单"
      width="500px"
    >
      <el-form
        :model="blacklistForm"
        label-width="100px"
      >
        <el-form-item label="IP地址" required>
          <el-input
            v-model="blacklistForm.ip"
            placeholder="请输入IP地址"
          />
        </el-form-item>
        <el-form-item label="用户名">
          <el-input
            v-model="blacklistForm.username"
            placeholder="请输入用户名（可选）"
          />
        </el-form-item>
        <el-form-item label="封禁原因" required>
          <el-input
            v-model="blacklistForm.reason"
            type="textarea"
            placeholder="请输入封禁原因"
            rows="3"
          />
        </el-form-item>
        <el-form-item label="封禁时长" required>
          <el-input-number
            v-model="blacklistForm.hours"
            :min="1"
            :max="168"
            placeholder="小时"
            style="width: 100%"
          />
          <div style="font-size: 12px; color: #666; margin-top: 5px;">
            封禁时长（1-168小时，即最多7天）
          </div>
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="blacklistDialogVisible = false" size="default">
            <i class="fas fa-times"></i>
            取消
          </el-button>
          <el-button type="primary" @click="addToBlacklist" size="default">
            <i class="fas fa-ban"></i>
            添加黑名单
          </el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 验证码弹出窗口 -->
    <div v-if="showCaptchaModal" class="captcha-overlay">
      <div class="captcha-modal">
        <div class="captcha-header">
          <h3>安全验证</h3>
          <div class="captcha-subtitle">请按数字顺序点击红点（共{{ count }}个）</div>
        </div>
        <div class="captcha-content">
          <div v-if="loading" class="captcha-loading">验证码加载中...</div>
          <div v-else class="captcha-image-container">
            <img :src="img" @click="onImgClick" ref="imgRef" class="captcha-image"/>
            <template v-for="(p, i) in clicks" :key="i">
              <span :style="pointStyle(p)" class="click-marker">x</span>
            </template>
          </div>
          <div class="captcha-actions">
            <el-button type="primary" @click="submit" :disabled="clicks.length!==count">
              提交验证 ({{ clicks.length }}/{{ count }})
            </el-button>
            <el-button @click="refreshCaptcha">刷新</el-button>
            <el-button @click="cancelLogin">取消</el-button>
          </div>
          <div v-if="msg" class="captcha-message" :style="{color:msgColor}">{{ msg }}</div>
        </div>
      </div>
  </div>

  <!-- 心跳监控弹窗 -->
  <el-dialog
    v-model="heartbeatDialog.visible"
    :title="`客户端心跳监控 - ${heartbeatDialog.clientId}`"
    width="600px"
    :before-close="closeHeartbeatMonitor"
  >
    <div class="heartbeat-monitor" :class="{ 'heartbeat-active': heartbeatDialog.isActive }">
      <div class="monitor-header">
        <div class="client-info">
          <i class="el-icon-monitor"></i>
          <span>实时心跳监控</span>
        </div>
        <div class="monitor-status">
          <el-tag :type="heartbeatDialog.isActive ? 'success' : 'danger'" size="small">
            {{ heartbeatDialog.isActive ? '活跃' : '离线' }}
          </el-tag>
        </div>
      </div>

      <div class="heartbeat-chart">
        <canvas
          ref="heartbeatCanvas"
          width="550"
          height="200"
          class="heartbeat-canvas"
        ></canvas>
      </div>

      <div class="heartbeat-stats">
        <div class="stat-item">
          <span class="stat-label">心跳次数:</span>
          <span class="stat-value">{{ heartbeatDialog.heartbeatCount }}</span>
        </div>
        <div class="stat-item">
          <span class="stat-label">最后心跳:</span>
          <span class="stat-value">{{ heartbeatDialog.lastHeartbeat || '无' }}</span>
        </div>
        <div class="stat-item">
          <span class="stat-label">监控时长:</span>
          <span class="stat-value">{{ heartbeatDialog.monitorDuration }}秒</span>
        </div>
      </div>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="closeHeartbeatMonitor" size="default">
          <i class="fas fa-times"></i>
          关闭
        </el-button>
        <el-button type="primary" @click="clearHeartbeatData" size="default">
          <i class="fas fa-trash"></i>
          清空数据
        </el-button>
        <el-button type="success" @click="testHeartbeat" size="default">
          <i class="fas fa-heartbeat"></i>
          测试心跳
        </el-button>
      </div>
    </template>
  </el-dialog>

  <!-- RSA证书生成对话框 -->
  <el-dialog
    v-model="showRSACertDialog"
    title="生成RSA证书"
    width="700px"
    :close-on-click-modal="false"
  >
    <el-form :model="rsaCertForm" label-width="120px" class="rsa-cert-form">
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="通用名称" required>
            <el-input v-model="rsaCertForm.common_name" placeholder="例如: example.com" />
            <div class="form-help">证书的主要域名或服务名称</div>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="组织名称">
            <el-input v-model="rsaCertForm.organization" placeholder="例如: My Company" />
            <div class="form-help">证书持有者的组织名称</div>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="组织单位">
            <el-input v-model="rsaCertForm.organization_unit" placeholder="例如: IT Department" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="国家代码">
            <el-input v-model="rsaCertForm.country" placeholder="例如: CN" maxlength="2" />
            <div class="form-help">两位国家代码</div>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="省份">
            <el-input v-model="rsaCertForm.province" placeholder="例如: Beijing" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="城市">
            <el-input v-model="rsaCertForm.locality" placeholder="例如: Beijing" />
          </el-form-item>
        </el-col>
      </el-row>

      <el-form-item label="DNS名称">
        <el-tag
          v-for="dns in rsaCertForm.dns_names"
          :key="dns"
          closable
          @close="removeDNSName(dns)"
          style="margin-right: 8px; margin-bottom: 4px;"
        >
          {{ dns }}
        </el-tag>
        <el-input
          v-if="inputDNSVisible"
          ref="inputDNSRef"
          v-model="inputDNSValue"
          size="small"
          style="width: 200px;"
          @keyup.enter="handleDNSInputConfirm"
          @blur="handleDNSInputConfirm"
        />
        <el-button v-else size="small" @click="showDNSInput">+ 添加DNS名称</el-button>
        <div class="form-help">证书支持的域名列表</div>
      </el-form-item>

      <el-form-item label="IP地址">
        <el-tag
          v-for="ip in rsaCertForm.ip_addresses"
          :key="ip"
          closable
          @close="removeIPAddress(ip)"
          style="margin-right: 8px; margin-bottom: 4px;"
        >
          {{ ip }}
        </el-tag>
        <el-input
          v-if="inputIPVisible"
          ref="inputIPRef"
          v-model="inputIPValue"
          size="small"
          style="width: 200px;"
          @keyup.enter="handleIPInputConfirm"
          @blur="handleIPInputConfirm"
        />
        <el-button v-else size="small" @click="showIPInput">+ 添加IP地址</el-button>
        <div class="form-help">证书支持的IP地址列表</div>
      </el-form-item>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="密钥长度">
            <el-select v-model="rsaCertForm.key_size" style="width: 100%;">
              <el-option label="2048位" :value="2048" />
              <el-option label="3072位" :value="3072" />
              <el-option label="4096位" :value="4096" />
            </el-select>
            <div class="form-help">更长的密钥更安全但性能较低</div>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="有效期">
            <el-select v-model="rsaCertForm.validity_period" style="width: 100%;">
              <el-option label="3个月" :value="{ months: 3, label: '3个月' }" />
              <el-option label="6个月" :value="{ months: 6, label: '6个月' }" />
              <el-option label="1年" :value="{ months: 12, label: '1年' }" />
              <el-option label="2年" :value="{ months: 24, label: '2年' }" />
              <el-option label="5年" :value="{ months: 60, label: '5年' }" />
              <el-option label="10年" :value="{ months: 120, label: '10年' }" />
            </el-select>
            <div class="form-help">证书有效期（推荐3个月定期更新）</div>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="showRSACertDialog = false" size="default">
          <i class="fas fa-times"></i>
          取消
        </el-button>
        <el-button type="primary" @click="generateRSACertificate" :loading="rsaCertGenerating" size="default">
          <i class="fas fa-certificate"></i>
          {{ rsaCertGenerating ? '生成中...' : '生成证书' }}
        </el-button>
      </div>
    </template>
  </el-dialog>

  <!-- 设备创建/编辑对话框 -->
  <el-dialog
    v-model="deviceDialogVisible"
    :title="isDeviceEditMode ? '编辑设备' : '新建设备'"
    width="800px"
    :close-on-click-modal="false"
    class="modern-dialog"
    top="5vh"
    :style="{ maxHeight: '85vh' }"
  >
    <div class="modern-form-container">
      <el-tabs v-model="activeDeviceTab" type="card" class="device-tabs">
        <!-- 基本信息标签页 -->
        <el-tab-pane label="基本信息" name="basic">
          <el-form :model="deviceForm" label-width="120px" class="modern-form">
            <div class="form-section">
              <div class="section-header">
                <h4 class="section-title">
                  <i class="fas fa-mobile-alt"></i>
                  设备基本信息
                </h4>
              </div>
              <div class="section-content">
                <el-row :gutter="20">
                  <el-col :span="12">
                    <el-form-item label="设备ID" required>
                      <el-input
                        v-model="deviceForm.device_id"
                        placeholder="请输入设备ID（与MQTT客户端ID相同）"
                        :disabled="isDeviceEditMode"
                      />
                      <div class="form-help">设备ID必须与MQTT客户端ID完全一致</div>
                    </el-form-item>
                  </el-col>
                  <el-col :span="12">
                    <el-form-item label="设备名称" required>
                      <el-input v-model="deviceForm.device_name" placeholder="请输入设备名称" />
                    </el-form-item>
                  </el-col>
                </el-row>

                <el-row :gutter="20">
                  <el-col :span="12">
                    <el-form-item label="归属用户" required>
                      <el-select v-model="deviceForm.user_id" placeholder="请选择用户" style="width: 100%;">
                        <el-option
                          v-for="user in users"
                          :key="user.id"
                          :label="user.username"
                          :value="user.id"
                        />
                      </el-select>
                    </el-form-item>
                  </el-col>
                  <el-col :span="12">
                    <el-form-item label="设备类型">
                      <el-select
                        v-model="deviceForm.device_type"
                        style="width: 100%;"
                        :placeholder="deviceTypesLoading ? '正在加载设备类型...' : (activeDeviceTypes.length === 0 ? '暂无可用设备类型' : '请选择设备类型')"
                        :loading="deviceTypesLoading"
                        :disabled="deviceTypesLoading"
                        filterable
                        :key="`device-type-select-${activeDeviceTypes.length}-${deviceForm.device_type}`"
                      >
                        <el-option
                          v-for="deviceType in activeDeviceTypes"
                          :key="deviceType.type_code"
                          :value="deviceType.type_code"
                          :label="deviceType.type_name"
                        >
                          <span style="display: flex; align-items: center; gap: 8px;">
                            <i :class="deviceType.icon" :style="{ color: deviceType.color, fontSize: '16px' }"></i>
                            <span>{{ deviceType.type_name }}</span>
                          </span>
                        </el-option>
                        <template v-if="activeDeviceTypes.length === 0 && !deviceTypesLoading" #empty>
                          <div style="padding: 20px; text-align: center; color: #999;">
                            <i class="fas fa-exclamation-triangle" style="margin-bottom: 8px; font-size: 24px;"></i>
                            <div>暂无可用的设备类型</div>
                            <el-button type="text" @click="switchToDeviceTypes" style="margin-top: 8px;">
                              <i class="fas fa-plus"></i>
                              创建设备类型
                            </el-button>
                          </div>
                        </template>
                      </el-select>
                    </el-form-item>
                  </el-col>
                </el-row>

                <el-row>
                  <el-col :span="24">
                    <el-form-item>
                      <el-checkbox v-model="deviceForm.is_active">启用设备</el-checkbox>
                    </el-form-item>
                  </el-col>
                </el-row>
              </div>
            </div>
          </el-form>
        </el-tab-pane>

        <!-- 授权配置标签页 -->
        <el-tab-pane label="授权配置" name="authorization">
          <el-form :model="deviceForm" label-width="120px" class="modern-form">
            <!-- 使用限制配置 -->
            <div class="form-section">
              <div class="section-header">
                <h4 class="section-title">
                  <i class="fas fa-clock"></i>
                  使用限制
                </h4>
              </div>
              <div class="section-content">
                <el-row :gutter="20">
                  <el-col :span="12">
                    <el-form-item label="最大使用次数">
                      <el-input-number
                        v-model="deviceForm.max_usage"
                        :min="0"
                        :max="999999"
                        style="width: 100%;"
                        placeholder="0表示无限制"
                      />
                      <div class="form-help">设置设备最大使用次数，0表示无限制</div>
                    </el-form-item>
                  </el-col>
                  <el-col :span="12">
                    <el-form-item label="过期时间">
                      <el-date-picker
                        v-model="deviceForm.expire_time"
                        type="datetime"
                        :placeholder="deviceForm.expire_time ? '点击修改过期时间' : '永不过期'"
                        style="width: 100%;"
                        :disabled-date="disabledDate"
                        :shortcuts="dateShortcuts"
                        clearable
                      />
                      <div class="form-help">清空设置为永不过期</div>
                    </el-form-item>
                  </el-col>
                </el-row>
              </div>
            </div>

            <!-- 安全配置 -->
            <div class="form-section">
              <div class="section-header">
                <h4 class="section-title">
                  <i class="fas fa-shield-alt"></i>
                  安全配置
                </h4>
              </div>
              <div class="section-content">
                <el-row>
                  <el-col :span="24">
                    <el-form-item>
                      <el-checkbox v-model="deviceForm.account_info_encryption">MQTT登录信息RSA加密</el-checkbox>
                      <div class="form-help">开启后MQTT成功登录发送的信息将使用RSA加密</div>
                    </el-form-item>
                  </el-col>
                </el-row>
              </div>
            </div>


          </el-form>
        </el-tab-pane>
      </el-tabs>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="deviceDialogVisible = false" size="default">
          <i class="fas fa-times"></i>
          取消
        </el-button>
        <el-button type="primary" @click="saveDevice" :loading="deviceSaveLoading" size="default">
          <i class="fas fa-save"></i>
          {{ deviceSaveLoading ? '保存中...' : '保存' }}
        </el-button>
      </div>
    </template>
  </el-dialog>

  <!-- 设备类型创建/编辑对话框 -->
  <el-dialog
    v-model="deviceTypeDialogVisible"
    :title="isDeviceTypeEditMode ? '编辑设备类型' : '新建设备类型'"
    width="600px"
    :close-on-click-modal="false"
    class="modern-dialog"
  >
    <div class="device-type-form">
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="类型代码" required>
            <el-input
              v-model="deviceTypeForm.type_code"
              placeholder="请输入类型代码"
              :disabled="isDeviceTypeEditMode"
            />
            <div class="form-help">类型代码用于系统内部标识，创建后不可修改</div>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="类型名称" required>
            <el-input
              v-model="deviceTypeForm.type_name"
              placeholder="请输入类型名称"
            />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="图标">
            <div class="icon-selector" :class="{ open: showIconSelector }" v-click-outside="closeIconSelector">
              <div class="selected-icon" @click="showIconSelector = !showIconSelector">
                <i :class="deviceTypeForm.icon" :style="{ color: deviceTypeForm.color }"></i>
                <span>{{ getIconName(deviceTypeForm.icon) }}</span>
                <i class="fas fa-chevron-down"></i>
              </div>
              <div v-if="showIconSelector" class="icon-dropdown">
                <div class="icon-search">
                  <el-input
                    v-model="iconSearchKeyword"
                    placeholder="搜索图标..."
                    size="small"
                    prefix-icon="el-icon-search"
                  />
                </div>
                <div class="icon-grid">
                  <div
                    v-for="icon in filteredIcons"
                    :key="icon.class"
                    class="icon-item"
                    :class="{ active: deviceTypeForm.icon === icon.class }"
                    @click="selectIcon(icon.class)"
                  >
                    <i :class="icon.class"></i>
                    <span class="icon-name">{{ icon.name }}</span>
                  </div>
                </div>
              </div>
            </div>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="颜色">
            <el-input
              v-model="deviceTypeForm.color"
              placeholder="请输入颜色值"
              type="color"
            />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="排序">
            <el-input-number
              v-model="deviceTypeForm.sort_order"
              :min="1"
              style="width: 100%;"
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item>
            <el-checkbox v-model="deviceTypeForm.is_active">启用此设备类型</el-checkbox>
          </el-form-item>
        </el-col>
      </el-row>

      <el-form-item label="描述">
        <el-input
          v-model="deviceTypeForm.description"
          type="textarea"
          :rows="3"
          placeholder="请输入设备类型描述"
        />
      </el-form-item>

      <!-- 预览 -->
      <div class="type-preview">
        <h4>预览效果：</h4>
        <div class="preview-item">
          <i :class="deviceTypeForm.icon" :style="{ color: deviceTypeForm.color }"></i>
          <span>{{ deviceTypeForm.type_name || '设备类型名称' }}</span>
        </div>
      </div>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="deviceTypeDialogVisible = false" size="default">
          <i class="fas fa-times"></i>
          取消
        </el-button>
        <el-button type="primary" @click="saveDeviceType" size="default">
          <i class="fas fa-save"></i>
          保存
        </el-button>
      </div>
    </template>
  </el-dialog>


</template>

<script setup>
import { ref, reactive, computed, onMounted, onUnmounted, watch, nextTick } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import axios from 'axios'
// import { forceApplyLightTheme, injectForceStyles } from '../utils/forceStyles.js'

// 配置axios默认baseURL - 使用相对路径让Vite代理处理
// 在开发环境下，Vite会将/api请求代理到后端服务器
// 在生产环境下，需要确保前端和后端部署在同一域名下，或者配置正确的代理
axios.defaults.baseURL = ''

// 调试日志控制
let enableDebugLogs = true

// 调试日志函数
const debugLog = (...args) => {
  if (enableDebugLogs) {
    console.log(...args)
  }
}

const debugError = (...args) => {
  if (enableDebugLogs) {
    console.error(...args)
  }
}

const debugWarn = (...args) => {
  if (enableDebugLogs) {
    console.warn(...args)
  }
}

// 移除滚动监听代码，因为新布局不需要滚动条

// 从服务器获取调试开关状态
const loadDebugSettings = async () => {
  try {
    const response = await axios.get('/debug/status')
    enableDebugLogs = response.data.debug_logs_enabled
    debugLog('🔧 调试日志已', enableDebugLogs ? '启用' : '禁用')
  } catch (error) {
    // 如果获取失败，在开发环境下默认启用
    enableDebugLogs = import.meta.env.DEV || import.meta.env.MODE === 'development'
    console.warn('无法获取调试设置，使用默认值:', enableDebugLogs)
  }
}

const img = ref('')
const id = ref('')
const count = ref(0)
const clicks = reactive([])
const loading = ref(false)
const msg = ref('')
const msgColor = ref('red')
const imgRef = ref(null)

// 移动端状态
const isMobile = ref(false)
const sidebarOpen = ref(false)

// 登录相关状态
const isLoggedIn = ref(false)
const username = ref('')
const remainingUses = ref(0)
const loginLoading = ref(false)
const loginForm = reactive({
  username: '',
  password: ''
})

// 验证码弹窗状态
const showCaptchaModal = ref(false)
const tempLoginData = ref(null)

// 后台管理状态
const activeMenu = ref('dashboard')

// 用户管理状态
const users = ref([])
const filteredUsers = ref([])
const usersLoading = ref(false)
const searchKeyword = ref('')
const totalUsers = ref(0)

// 批量选择状态
const selectedUsers = ref([])
const batchActionLoading = ref(false)

// 分页状态
const userPagination = reactive({
  page: 1,
  pageSize: 20,
  total: 0,
  totalPages: 0
})

// 用户表单状态
const userDialogVisible = ref(false)
const isEditMode = ref(false)
const saveLoading = ref(false)
const userFormRef = ref(null)
const userFormKey = ref(0) // 用于强制重新渲染表单
// 用户编辑对话框不再使用标签页，改为卡片式布局

// 用户编辑对话框现在使用固定的样式和响应式CSS
const userForm = reactive({
  id: null,
  username: '',
  password: '',
  remark: '',
  can_login_backend: false,
  can_login_mqtt: true,
  is_active: true
})

// MQTT服务器状态
const mqttStats = ref({
  port: 1883,
  running: false,
  clients: 0,
  topics: 0,
  client_list: [],
  topic_list: [],
  blacklist: [],
  failed_auth: [],
  config: {
    require_auth: true,
    allow_anonymous: false,
    max_failed_attempts: 3,
    blacklist_duration: 24,
    cleanup_interval: 30
  }
})

// 设备管理状态
const devices = ref([])
const devicesLoading = ref(false)
const selectedDeviceIds = ref([])
const deviceSearchKeyword = ref('')
const deviceForm = reactive({
  id: null,
  device_id: '',
  device_name: '',
  user_id: null,
  device_type: '', // 初始为空，等待加载设备类型后设置
  is_active: true,
  // 授权配置
  max_usage: 100,
  expire_time: null,
  account_info_encryption: true // MQTT登录信息RSA加密开关
})
const deviceDialogVisible = ref(false)
const isDeviceEditMode = ref(false)
const deviceSaveLoading = ref(false)
const activeDeviceTab = ref('basic')
const activeDeviceSubTab = ref('device-list')

// 设备类型管理状态
const deviceTypes = ref([])
const activeDeviceTypes = ref([])
const deviceTypesLoading = ref(false)
const deviceTypeDialogVisible = ref(false)
const isDeviceTypeEditMode = ref(false)
const deviceTypesLastLoaded = ref(0) // 记录上次加载时间
const deviceTypeForm = reactive({
  id: null,
  type_code: '',
  type_name: '',
  description: '',
  icon: 'fas fa-microchip',
  color: '#6b7280',
  is_active: true,
  sort_order: 0
})

// 图标选择器相关数据
const showIconSelector = ref(false)
const iconSearchKeyword = ref('')

// 可选图标列表
const availableIcons = [
  { class: 'fas fa-desktop', name: '桌面电脑' },
  { class: 'fas fa-laptop', name: '笔记本电脑' },
  { class: 'fas fa-mobile-alt', name: '手机' },
  { class: 'fas fa-tablet-alt', name: '平板' },
  { class: 'fas fa-tv', name: '电视/显示器' },
  { class: 'fas fa-microchip', name: '芯片/处理器' },
  { class: 'fas fa-server', name: '服务器' },
  { class: 'fas fa-network-wired', name: '网络设备' },
  { class: 'fas fa-wifi', name: 'WiFi设备' },
  { class: 'fas fa-router', name: '路由器' },
  { class: 'fas fa-video', name: '摄像头' },
  { class: 'fas fa-camera', name: '相机' },
  { class: 'fas fa-print', name: '打印机' },
  { class: 'fas fa-scanner', name: '扫描仪' },
  { class: 'fas fa-keyboard', name: '键盘' },
  { class: 'fas fa-mouse', name: '鼠标' },
  { class: 'fas fa-headphones', name: '耳机' },
  { class: 'fas fa-microphone', name: '麦克风' },
  { class: 'fas fa-speaker', name: '扬声器' },
  { class: 'fas fa-thermometer-half', name: '温度传感器' },
  { class: 'fas fa-tachometer-alt', name: '仪表盘' },
  { class: 'fas fa-lightbulb', name: '灯泡' },
  { class: 'fas fa-plug', name: '插头/电源' },
  { class: 'fas fa-battery-full', name: '电池' },
  { class: 'fas fa-car', name: '汽车' },
  { class: 'fas fa-truck', name: '卡车' },
  { class: 'fas fa-plane', name: '飞机' },
  { class: 'fas fa-ship', name: '船舶' },
  { class: 'fas fa-train', name: '火车' },
  { class: 'fas fa-bicycle', name: '自行车' },
  { class: 'fas fa-home', name: '家居设备' },
  { class: 'fas fa-building', name: '建筑设备' },
  { class: 'fas fa-industry', name: '工业设备' },
  { class: 'fas fa-tools', name: '工具设备' },
  { class: 'fas fa-cogs', name: '机械设备' },
  { class: 'fas fa-robot', name: '机器人' },
  { class: 'fas fa-gamepad', name: '游戏设备' },
  { class: 'fas fa-watch', name: '智能手表' },
  { class: 'fas fa-glasses', name: 'AR/VR设备' },
  { class: 'fas fa-satellite', name: '卫星设备' },
  { class: 'fas fa-broadcast-tower', name: '信号塔' },
  { class: 'fas fa-qrcode', name: '扫码设备' },
  { class: 'fas fa-barcode', name: '条码设备' },
  { class: 'fas fa-fingerprint', name: '指纹设备' },
  { class: 'fas fa-key', name: '安全设备' },
  { class: 'fas fa-shield-alt', name: '防护设备' },
  { class: 'fas fa-fire', name: '消防设备' },
  { class: 'fas fa-medkit', name: '医疗设备' },
  { class: 'fas fa-heartbeat', name: '监护设备' },
  { class: 'fas fa-atom', name: '科学设备' },
  { class: 'fas fa-flask', name: '实验设备' },
  { class: 'fas fa-microscope', name: '显微镜' },
  { class: 'fas fa-question-circle', name: '其他设备' }
]

// 字段级编辑状态跟踪
const editingFields = ref(new Set())
const userEditTimestamps = ref(new Map())

// 编辑状态管理函数
function startEditing(fieldName) {
  editingFields.value.add(fieldName)
  userEditTimestamps.value.set(fieldName, Date.now())
  debugLog('🖊️ 开始编辑字段:', fieldName)
}

function stopEditing(fieldName) {
  editingFields.value.delete(fieldName)
  userEditTimestamps.value.delete(fieldName)
  debugLog('✅ 完成编辑字段:', fieldName)
}

function isFieldBeingEdited(fieldName) {
  const isEditing = editingFields.value.has(fieldName)
  const lastEditTime = userEditTimestamps.value.get(fieldName)
  const timeSinceEdit = lastEditTime ? Date.now() - lastEditTime : Infinity

  // 如果正在编辑或者5秒内编辑过，认为字段正在被编辑
  return isEditing || timeSinceEdit < 5000
}

// 主题管理相关数据
const topicFilter = ref('')
const processedTopics = ref([])

// 心跳监控相关数据
const heartbeatDialog = reactive({
  visible: false,
  clientId: '',
  isActive: false,
  heartbeatCount: 0,
  lastHeartbeat: '',
  monitorDuration: 0,
  startTime: null
})

const heartbeatCanvas = ref(null)
let heartbeatChart = null
let heartbeatData = []
let animationId = null
let monitorTimer = null
let lastActiveTime = null
let eventSource = null // SSE连接

// MQTT消息发布表单
const publishForm = reactive({
  topic: '',
  message: '',
  qos: 0,
  retain: false
})
const publishLoading = ref(false)

// MQTT单发消息表单
const sendForm = reactive({
  clientId: '',
  topic: '',
  message: '',
  qos: 0,
  retain: false
})
const sendLoading = ref(false)

// 选中客户端的订阅主题
const selectedClientTopics = ref([])
const activeTab = ref('broadcast')

// MQTT配置表单
const mqttConfig = reactive({
  require_auth: true,
  allow_anonymous: false,
  max_failed_attempts: 3,
  blacklist_duration: 24,
  cleanup_interval: 30,
  heartbeat_interval: 15,
  heartbeat_timeout: 60,
  heartbeat_packet_interval: 1000
})
const configLoading = ref(false)

// MQTT管理界面状态
const mqttActiveTab = ref('clients')

// 保持消息管理
const retainedMessages = ref([])
const retainedLoading = ref(false)
const clearRetainedLoading = ref(false)
const deleteRetainedLoading = ref({})

// 黑名单管理
const blacklistDialogVisible = ref(false)
const blacklistForm = reactive({
  ip: '',
  username: '',
  reason: '',
  hours: 24
})

// 遗嘱消息管理
const willMessages = ref([])
const willLoading = ref(false)
const deleteWillLoading = ref({})

// 操作日志相关数据变量
const logs = ref([])
const logStats = ref({
  today_count: 0,
  success_rate: 0,
  operation_types: {}
})

// 日期选择器相关配置
const disabledDate = (time) => {
  // 禁用过去的日期（只能选择今天及以后的日期）
  return time.getTime() < Date.now() - 8.64e7 // 减去一天的毫秒数，允许选择今天
}

const dateShortcuts = [
  {
    text: '1个月后',
    value: () => {
      const date = new Date()
      date.setMonth(date.getMonth() + 1)
      return date
    }
  },
  {
    text: '3个月后',
    value: () => {
      const date = new Date()
      date.setMonth(date.getMonth() + 3)
      return date
    }
  },
  {
    text: '6个月后',
    value: () => {
      const date = new Date()
      date.setMonth(date.getMonth() + 6)
      return date
    }
  },
  {
    text: '1年后',
    value: () => {
      const date = new Date()
      date.setFullYear(date.getFullYear() + 1)
      return date
    }
  }
]
const logFilters = reactive({
  operation_type: '',
  module: '',
  keyword: ''
})
const logPagination = reactive({
  page: 1,
  total_pages: 1,
  total_count: 0,
  page_size: 20
})
const logsLoading = ref(false)

// 用户表单验证规则
const userFormRules = reactive({
  username: [
    { required: true, message: '请输入用户名', trigger: 'blur' },
    { min: 3, max: 20, message: '用户名长度应在3到20个字符之间', trigger: 'blur' }
  ],
  password: [
    { 
      validator: (rule, value, callback) => {
        // 编辑模式下，密码是可选的
        if (isEditMode.value) {
          // 如果是编辑模式，密码为空时允许通过（保持原密码）
          if (!value || value === '') {
            callback()
            return
          }
          // 如果编辑模式下输入了密码，检查长度
          if (value.length < 6) {
            callback(new Error('密码长度至少6个字符'))
            return
          }
          callback()
        } else {
          // 新建模式下，密码是必填的
          if (!value || value === '') {
            callback(new Error('请输入密码'))
            return
          }
          if (value.length < 6) {
            callback(new Error('密码长度至少6个字符'))
            return
          }
          callback()
        }
      },
      trigger: 'blur'
    }
  ]
})

// 系统设置相关数据
const activeSettingsTab = ref('cors')
const configSaving = ref(false)

// 安全配置数据
const securityConfig = reactive({
  cors_allowed_origins: 'http://localhost:5173,http://127.0.0.1:5173,http://***********:5173',
  cors_allowed_methods: 'GET,POST,PUT,DELETE,OPTIONS',
  cors_allowed_headers: 'Origin,Content-Type,Authorization',
  cors_allow_credentials: true,
  cors_max_age: 86400,
  cors_dev_mode: false
})

const rateLimitConfig = reactive({
  enable_rate_limit: true,
  requests_per_minute: 60,
  burst_size: 10,
  cleanup_interval: 300
})

const databaseConfig = reactive({
  max_open_conns: 25,
  max_idle_conns: 5,
  conn_max_lifetime: 300,
  conn_max_idle_time: 60
})

const validationConfig = reactive({
  max_username_length: 50,
  max_password_length: 128,
  max_phone_length: 20,
  max_remark_length: 200,
  enable_xss_protection: true
})

const captchaConfig = reactive({
  img_width: 300,
  img_height: 150,
  point_radius: 20,
  tolerance_radius: 20,
  min_distance: 80,
  num_points: 3,
  expire_minutes: 3,
  debug_output: false
})

const debugConfig = reactive({
  debug_mode: false,
  frontend_debug_logs: false
})

// RSA证书管理相关数据
const certificateLoading = ref(false)
const rsaCertLoading = ref(false)

// RSA证书管理相关数据
const rsaCertInfo = ref({
  key_pair_initialized: false,
  key_info: {},
  rsa_certificates: [],
  total_rsa_certs: 0
})
const showRSACertDialog = ref(false)
const rsaCertGenerating = ref(false)
const rsaCertForm = reactive({
  common_name: 'IPC Management System',
  organization: 'IPC Management',
  organization_unit: 'IT Department',
  country: 'CN',
  province: 'Beijing',
  locality: 'Beijing',
  dns_names: ['localhost'],
  ip_addresses: ['127.0.0.1'],
  key_size: 2048,
  validity_period: { months: 3, label: '3个月' } // 默认3个月
})

// DNS和IP输入相关
const inputDNSVisible = ref(false)
const inputDNSValue = ref('')
const inputDNSRef = ref(null)
const inputIPVisible = ref(false)
const inputIPValue = ref('')
const inputIPRef = ref(null)

// 从localStorage获取token
const getToken = () => localStorage.getItem('auth_token')
const setToken = (token) => localStorage.setItem('auth_token', token)
const removeToken = () => localStorage.removeItem('auth_token')

// 设置axios默认headers
const setAuthHeader = (token) => {
  if (token) {
    axios.defaults.headers.common['Authorization'] = `Bearer ${token}`
  } else {
    delete axios.defaults.headers.common['Authorization']
  }
}

// 主题处理相关计算属性和函数
const filteredTopics = computed(() => {
  if (!processedTopics.value) return []

  if (!topicFilter.value) {
    return processedTopics.value
  }

  return processedTopics.value.filter(topic =>
    topic.pattern.toLowerCase().includes(topicFilter.value.toLowerCase())
  )
})

// RSA证书管理相关计算属性
const rsaCertificateList = computed(() => {
  return rsaCertInfo.value.rsa_certificates || []
})

// 主题分组处理函数
function processTopics(rawTopics) {
  if (!rawTopics || rawTopics.length === 0) {
    return []
  }
  
  // 分组模式
  const groups = new Map()
  const standalone = []
  
  rawTopics.forEach(topic => {
    const pattern = extractTopicPattern(topic)
    if (pattern && pattern !== topic && groups.has(pattern)) {
      // 动态主题，加入已有分组
      groups.get(pattern).topics.push(topic)
      groups.get(pattern).count++
    } else if (pattern && pattern !== topic) {
      // 动态主题，创建新分组
      groups.set(pattern, {
        pattern: pattern + '/*',
        type: 'dynamic',
        count: 1,
        topics: [topic]
      })
    } else {
      // 静态主题，单独显示
      standalone.push({
        pattern: topic,
        type: 'static',
        count: 1,
        topics: [topic]
      })
    }
  })
  
  return [...Array.from(groups.values()), ...standalone]
}

// 提取主题模式（识别动态主题前缀）
function extractTopicPattern(topic) {
  // 识别常见的动态主题模式
  const patterns = [
    /^(account)\/[a-zA-Z0-9_]+$/,  // account/client_id
    /^(ipc\/[^\/]+)\/[a-zA-Z0-9_]+$/,  // ipc/xxx/client_id
    /^([^\/]+)\/[a-zA-Z0-9_]{10,}$/  // prefix/long_id
  ]
  
  for (const pattern of patterns) {
    const match = topic.match(pattern)
    if (match) {
      return match[1]
    }
  }
  
  return topic  // 返回原主题表示不是动态主题
}





// 更新处理后的主题列表
function updateProcessedTopics() {
  processedTopics.value = processTopics(mqttStats.value.topic_list)
}

// 心跳监控相关函数
function showHeartbeatMonitor(clientId) {
  debugLog('Opening heartbeat monitor for client:', clientId)
  debugLog('Current MQTT stats:', mqttStats.value)

  heartbeatDialog.visible = true
  heartbeatDialog.clientId = clientId
  heartbeatDialog.isActive = false // 初始设为false，等检测到活跃状态再设为true
  heartbeatDialog.heartbeatCount = 0
  heartbeatDialog.lastHeartbeat = ''
  heartbeatDialog.monitorDuration = 0
  heartbeatDialog.startTime = null // 将在startHeartbeatMonitoring中设置

  // 清空之前的数据
  heartbeatData = []
  lastActiveTime = null

  // 初始化画布
  nextTick(() => {
    initHeartbeatChart()
    startHeartbeatMonitoring()
  })
}

function closeHeartbeatMonitor() {
  heartbeatDialog.visible = false
  stopHeartbeatMonitoring()
}

function clearHeartbeatData() {
  heartbeatDialog.heartbeatCount = 0
  heartbeatDialog.lastHeartbeat = ''
  heartbeatData = []
  if (heartbeatChart) {
    drawHeartbeatChart()
  }
}

function testHeartbeat() {
  console.log('🧪 Testing heartbeat animation')
  addHeartbeat(1.5) // 添加一个测试心跳，强度稍高
}

function initHeartbeatChart() {
  if (!heartbeatCanvas.value) {
    debugError('Canvas element not found!')
    return
  }

  const canvas = heartbeatCanvas.value
  const ctx = canvas.getContext('2d')

  debugLog('Initializing heartbeat chart:', canvas.width, 'x', canvas.height)

  heartbeatChart = {
    canvas,
    ctx,
    width: canvas.width,
    height: canvas.height,
    centerY: canvas.height / 2,
    timeOffset: 0
  }

  // 清空之前的动画
  if (animationId) {
    cancelAnimationFrame(animationId)
  }

  // 开始绘制动画
  drawHeartbeatChart()
  debugLog('Heartbeat chart animation started')
}

function drawHeartbeatChart() {
  // 如果窗口已关闭，停止绘制
  if (!heartbeatChart || !heartbeatDialog.visible) {
    animationId = null
    return
  }

  const { ctx, width, height, centerY } = heartbeatChart

  // 清空画布
  ctx.clearRect(0, 0, width, height)

  // 绘制背景网格
  drawGrid(ctx, width, height)

  // 绘制基线
  ctx.strokeStyle = '#409eff'
  ctx.lineWidth = 2
  ctx.beginPath()
  ctx.moveTo(0, centerY)
  ctx.lineTo(width, centerY)
  ctx.stroke()

  // 绘制心跳波形
  if (heartbeatData.length > 0) {
    drawHeartbeatWave(ctx, width, height, centerY)
  }

  // 恢复60fps流畅动画
  if (heartbeatDialog.visible) {
    animationId = requestAnimationFrame(drawHeartbeatChart)
  }
}

function drawGrid(ctx, width, height) {
  ctx.strokeStyle = '#f0f0f0'
  ctx.lineWidth = 1

  // 垂直网格线
  for (let x = 0; x <= width; x += 50) {
    ctx.beginPath()
    ctx.moveTo(x, 0)
    ctx.lineTo(x, height)
    ctx.stroke()
  }

  // 水平网格线
  for (let y = 0; y <= height; y += 25) {
    ctx.beginPath()
    ctx.moveTo(0, y)
    ctx.lineTo(width, y)
    ctx.stroke()
  }
}

function drawHeartbeatWave(ctx, width, height, centerY) {
  const now = Date.now()
  const timeWindow = 10000 // 显示最近10秒的数据
  const pixelsPerSecond = width / (timeWindow / 1000)

  if (heartbeatData.length === 0) {
    return // 没有心跳数据
  }

  console.log(`Drawing ${heartbeatData.length} heartbeat points`)

  ctx.strokeStyle = '#67c23a'
  ctx.lineWidth = 3

  // 绘制每个心跳点
  heartbeatData.forEach((heartbeat, index) => {
    const timeDiff = now - heartbeat.timestamp
    if (timeDiff > timeWindow) return // 超出时间窗口

    const x = width - (timeDiff / 1000) * pixelsPerSecond

    if (x < 0 || x > width) return

    // 绘制心跳波形
    const wavePoints = generateHeartbeatWave(x, centerY, heartbeat.intensity)

    ctx.beginPath()
    ctx.moveTo(wavePoints[0].x, wavePoints[0].y)

    for (let i = 1; i < wavePoints.length; i++) {
      ctx.lineTo(wavePoints[i].x, wavePoints[i].y)
    }

    ctx.stroke()
  })
}

function generateHeartbeatWave(centerX, centerY, intensity = 1) {
  const amplitude = 60 * intensity
  const width = 30

  return [
    { x: centerX - width, y: centerY },
    { x: centerX - width/2, y: centerY },
    { x: centerX - width/4, y: centerY - amplitude * 0.3 },
    { x: centerX - width/8, y: centerY + amplitude * 0.8 },
    { x: centerX, y: centerY - amplitude },
    { x: centerX + width/8, y: centerY + amplitude * 0.4 },
    { x: centerX + width/4, y: centerY - amplitude * 0.2 },
    { x: centerX + width/2, y: centerY },
    { x: centerX + width, y: centerY }
  ]
}

function startHeartbeatMonitoring() {
  console.log('🚀 Starting simplified heartbeat monitoring for:', heartbeatDialog.clientId)

  // 调试：检查登录状态和token
  console.log('🔍 Debug - isLoggedIn:', isLoggedIn.value)
  console.log('🔍 Debug - token exists:', !!getToken())
  console.log('🔍 Debug - username:', username.value)

  // 设置监控开始时间
  heartbeatDialog.startTime = Date.now()

  // 重置计数器
  heartbeatDialog.heartbeatCount = 0

  // 建立SSE连接监听心跳事件
  connectToSSE()

  // 更新监控时长的定时器
  monitorTimer = setInterval(() => {
    if (heartbeatDialog.startTime) {
      heartbeatDialog.monitorDuration = Math.floor((Date.now() - heartbeatDialog.startTime) / 1000)
    }

    // 检查客户端活跃状态
    checkClientHeartbeat()
  }, 1000)

  console.log('💡 心跳监控已启动，将监听服务器的 client_heartbeat 事件')
}

function checkClientHeartbeat() {
  const currentClient = mqttStats.value.client_list?.find(client => client.id === heartbeatDialog.clientId)
  if (!currentClient) {
    heartbeatDialog.isActive = false
    return
  }

  // 简单检查客户端活跃状态
  try {
    const lastPingTime = currentClient.last_ping
    const now = Date.now()
    const timeSinceLastPing = now - lastPingTime

    // 更新最后心跳显示时间
    heartbeatDialog.lastHeartbeat = new Date(lastPingTime).toLocaleTimeString()

    // 使用与后端一致的60秒超时时间判断活跃状态
    heartbeatDialog.isActive = timeSinceLastPing < 60000

  } catch (error) {
    console.error('Error checking client heartbeat:', error)
    heartbeatDialog.isActive = false
  }
}

function stopHeartbeatMonitoring() {
  if (monitorTimer) {
    clearInterval(monitorTimer)
    monitorTimer = null
  }

  // 清理动画ID
  if (animationId) {
    cancelAnimationFrame(animationId)
    animationId = null
  }

  // 关闭SSE连接
  disconnectFromSSE()

  console.log('Heartbeat monitoring stopped')
}

function addHeartbeat(intensity = 1) {
  const now = Date.now()

  console.log('💓 Adding heartbeat to chart, intensity:', intensity)

  heartbeatData.push({
    timestamp: now,
    intensity
  })

  // 只保留最近的心跳数据
  heartbeatData = heartbeatData.filter(h => now - h.timestamp < 10000)

  // 更新统计信息
  heartbeatDialog.heartbeatCount++
  // 移除时间更新，统一由 checkClientHeartbeat() 函数管理时间显示

  console.log(`Heartbeat count: ${heartbeatDialog.heartbeatCount}, Data points: ${heartbeatData.length}`)

  // 移除重复的超时检查逻辑，统一由 checkClientHeartbeat() 函数处理
}

// 处理真实的心跳事件 - 简化版本
function handleHeartbeatEvent(eventData) {
  console.log('💓 服务器心跳事件:', eventData)

  // 更新客户端列表中的 last_ping 时间
  if (mqttStats.value.client_list && eventData.timestamp && eventData.client_id) {
    const clientIndex = mqttStats.value.client_list.findIndex(client =>
      client.id === eventData.client_id
    )
    if (clientIndex !== -1) {
      // 更新客户端的最后心跳时间（转换为毫秒）
      mqttStats.value.client_list[clientIndex].last_ping = eventData.timestamp * 1000
      debugLog('🔄 更新客户端心跳时间:', eventData.client_id, new Date(eventData.timestamp * 1000))
    }
  }

  // 只处理当前监控的客户端的心跳
  if (!heartbeatDialog.visible || eventData.client_id !== heartbeatDialog.clientId) {
    return
  }

  // 简单直接：每收到一个服务器心跳事件，就显示一次心跳动画
  addHeartbeat(1)

  console.log(`✅ 心跳计数: ${heartbeatDialog.heartbeatCount}`)
}

// 监听主题列表变化
watch(() => mqttStats.value.topic_list, () => {
  updateProcessedTopics()
}, { deep: true })

function pointStyle(p) {
  return {
    position: 'absolute',
    left: p.x - 6 + 'px',
    top: p.y - 6 + 'px',
    width: '12px',
    height: '12px',
    background: 'blue',
    borderRadius: '50%',
    border: '2px solid #fff',
    pointerEvents: 'none',
    boxShadow: '0 0 2px #0008'
  }
}

// 检查登录状态
async function checkAuthStatus() {
  const token = getToken()
  if (!token) {
    isLoggedIn.value = false
    return
  }
  
  setAuthHeader(token)
  try {
    const res = await axios.get('/api/auth/status')
    isLoggedIn.value = true
    remainingUses.value = res.data.remaining_uses

    // 从token中解析用户名（简单实现）
    const payload = JSON.parse(atob(token.split('.')[1]))
    username.value = payload.username

    // 自动登录成功后检查调试模式
    checkDebugMode().catch(err => {
      console.warn('Failed to check debug mode after auto login:', err)
    })

    // 自动登录成功后也建立SSE连接
    connectToSSE()
  } catch (e) {
    // token无效，清除登录状态
    logout()
  }
}


// 处理登录
async function handleLogin() {
  if (!loginForm.username || !loginForm.password) {
    ElMessage.error('请输入用户名和密码')
    return
  }
  
  loginLoading.value = true
  
  // 直接加载验证码，不先验证账号密码
  try {
    const res = await axios.post('/api/captcha/prepare', {
      username: loginForm.username,
      password: loginForm.password
    })
    
    // 显示验证码
    img.value = res.data.img
    id.value = res.data.id
    count.value = res.data.count
    clicks.splice(0)
    msg.value = ''
    
    // 临时存储登录信息，等验证码通过后再验证
    tempLoginData.value = {
      username: loginForm.username,
      password: loginForm.password
    }
    showCaptchaModal.value = true
    
  } catch (e) {
    const errorMsg = e.response?.data?.error || '获取验证码失败'
    ElMessage.error(errorMsg)
  }
  loginLoading.value = false
}

// 取消登录
function cancelLogin() {
  tempLoginData.value = null
  showCaptchaModal.value = false
  img.value = ''
  clicks.splice(0)
  msg.value = ''
  loginLoading.value = false
}

// 退出登录
function logout() {
  removeToken()
  setAuthHeader(null)
  isLoggedIn.value = false
  username.value = ''
  remainingUses.value = 0
  img.value = ''
  tempLoginData.value = null
  showCaptchaModal.value = false

  // 断开SSE连接
  disconnectFromSSE()

  ElMessage.info('已退出登录')
}


function onImgClick(e) {
  const rect = imgRef.value.getBoundingClientRect()
  
  // 坐标转换
  const scaleX = imgRef.value.naturalWidth / rect.width
  const scaleY = imgRef.value.naturalHeight / rect.height
  const x = Math.round((e.clientX - rect.left) * scaleX)
  const y = Math.round((e.clientY - rect.top) * scaleY)
  
  
  if (clicks.length >= count.value) return
  clicks.push({ x, y })
}

async function submit() {
  try {
    // 如果有临时登录数据，说明是登录流程
    if (tempLoginData.value) {
      const res = await axios.post('/api/captcha/verify-login', {
        id: id.value,
        clicks: clicks,
        username: tempLoginData.value.username,
        password: tempLoginData.value.password
      })
      
      if (res.data.success) {
        // 登录成功
        const token = res.data.token
        setToken(token)
        setAuthHeader(token)
        
        isLoggedIn.value = true
        username.value = res.data.user.username
        remainingUses.value = res.data.auth.remaining_uses

        // 清除临时数据
        tempLoginData.value = null

        // 登录成功后立即检查调试模式
        checkDebugMode().catch(err => {
          console.warn('Failed to check debug mode after login:', err)
        })

        // 登录成功后立即加载RSA证书信息
        loadRSACertificateInfo().catch(err => {
          console.warn('Failed to load RSA certificate info after login:', err)
        })
        showCaptchaModal.value = false

        // 登录成功后立即建立SSE连接
        connectToSSE()

        // 显示成功消息
        ElMessage.success('登录成功')
        
      }
    }
  } catch (e) {
    if (e.response?.status === 401) {
      const errorMsg = e.response?.data?.error || '认证失败'
      ElMessage.error(errorMsg)
      tempLoginData.value = null
      showCaptchaModal.value = false
    } else {
      const errorMsg = e.response?.data?.msg || e.response?.data?.error || '验证失败'
      msg.value = errorMsg
      msgColor.value = 'red'
    }
  }
}


// 在登录流程中刷新验证码
async function refreshCaptcha() {
  if (!tempLoginData.value) return
  
  loading.value = true
  msg.value = ''
  clicks.splice(0)
  
  try {
    const res = await axios.post('/api/captcha/prepare', {
      username: tempLoginData.value.username,
      password: tempLoginData.value.password
    })
    
    // 更新验证码
    img.value = res.data.img
    id.value = res.data.id
    count.value = res.data.count
    
  } catch (e) {
    const errorMsg = e.response?.data?.error || '获取验证码失败'
    ElMessage.error(errorMsg)
  }
  loading.value = false
}


// 菜单处理函数
function handleMenuSelect(key) {
  activeMenu.value = key

  // 移动端点击菜单后关闭侧边栏
  if (isMobile.value) {
    closeSidebar()
  }

  if (key === 'dashboard') {
    // 切换到仪表盘时加载相关数据
    loadDashboardData()
  } else if (key === 'users') {
    // 切换到用户管理页面时加载用户列表
    loadUsers()
  } else if (key === 'devices') {
    // 切换到设备管理页面时加载设备列表
    loadDevices()
  } else if (key === 'mqtt') {
    // 切换到MQTT管理页面时加载MQTT状态（仅初始加载，后续通过SSE实时更新）
    loadMqttStats()

    // 如果当前在保持消息标签，也加载保持消息
    if (mqttActiveTab.value === 'retained') {
      loadRetainedMessages()
    }
    // 如果当前在遗嘱管理标签，也加载遗嘱消息
    if (mqttActiveTab.value === 'will') {
      loadWillMessages()
    }
  } else if (key === 'logs') {
    // 切换到操作日志页面时加载日志
    loadLogs()
    loadLogStats()
  }
}

// 仪表盘数据加载函数
async function loadDashboardData() {
  // 加载用户数据
  loadUsers()
  // 加载MQTT状态
  loadMqttStats()
}

function getMenuTitle(key) {
  const titles = {
    dashboard: '仪表盘',
    users: '用户管理',
    devices: '设备管理',
    mqtt: 'MQTT管理',
    logs: '操作日志',
    settings: '系统设置'
  }
  return titles[key] || '未知页面'
}

// 用户管理函数
async function loadUsers() {
  usersLoading.value = true
  try {
    const params = new URLSearchParams({
      page: userPagination.page.toString(),
      page_size: userPagination.pageSize.toString()
    })
    
    if (searchKeyword.value) {
      params.append('keyword', searchKeyword.value)
    }
    
    const res = await axios.get(`/api/users?${params}`)
    users.value = res.data.users || []
    filteredUsers.value = [...users.value]
    userPagination.total = res.data.total || 0
    userPagination.totalPages = res.data.total_pages || 0
    totalUsers.value = userPagination.total
  } catch (e) {
    console.error('加载用户列表失败:', e)
    ElMessage.error('加载用户列表失败: ' + (e.response?.data?.error || e.message))
    // 如果加载失败，清空用户列表
    users.value = []
    filteredUsers.value = []
    userPagination.total = 0
    userPagination.totalPages = 0
    totalUsers.value = 0
  } finally {
    usersLoading.value = false
  }
}

function handleSearch() {
  userPagination.page = 1 // 搜索时重置到第一页
  loadUsers()
}

// 分页函数
function handlePageChange(page) {
  userPagination.page = page
  loadUsers()
}

function handlePageSizeChange(pageSize) {
  userPagination.pageSize = pageSize
  userPagination.page = 1
  loadUsers()
}

function showAddUserDialog() {
  // 重置编辑模式
  isEditMode.value = false

  // 强制重新渲染表单组件（防止浏览器自动填充）
  userFormKey.value++

  // 重置表单数据
  Object.assign(userForm, {
    id: null,
    username: '',
    password: '',
    remark: '',
    can_login_backend: false,
    can_login_mqtt: true,
    is_active: true
  })

  // 使用 nextTick 确保数据更新后再显示对话框
  nextTick(() => {
    userDialogVisible.value = true

    // 清除表单验证状态
    setTimeout(() => {
      if (userFormRef.value) {
        userFormRef.value.clearValidate()
      }
    }, 100)
  })
}

function editUser(user) {
  isEditMode.value = true
  userForm.id = user.id
  userForm.username = user.username
  userForm.password = ''
  userForm.remark = user.remark || ''
  
  userForm.can_login_backend = user.can_login_backend !== undefined ? user.can_login_backend : false
  userForm.can_login_mqtt = user.can_login_mqtt !== undefined ? user.can_login_mqtt : true
  userForm.is_active = user.is_active
  
  userDialogVisible.value = true
  
  // 清除表单验证状态
  setTimeout(() => {
    if (userFormRef.value) {
      userFormRef.value.clearValidate()
    }
  }, 100)
}


async function saveUser() {
  if (!userFormRef.value) return
  
  await userFormRef.value.validate(async (valid) => {
    if (valid) {
      saveLoading.value = true
      try {
        // 准备要发送的数据
        const userData = { ...userForm }
        
        // 如果是编辑模式且密码为空，则不发送密码字段
        if (isEditMode.value && !userData.password) {
          delete userData.password
        }
        
        if (isEditMode.value) {
          await axios.put(`/api/users/${userForm.id}`, userData)
          ElMessage.success('用户更新成功')
        } else {
          await axios.post('/api/users', userData)
          ElMessage.success('用户创建成功')
        }
        userDialogVisible.value = false
        loadUsers()
      } catch (e) {
        ElMessage.error(e.response?.data?.error || '操作失败')
      }
      saveLoading.value = false
    }
  })
}

// 获取删除按钮的提示信息
function getDeleteButtonTooltip(user) {
  if (user.username === username.value) {
    return '不能删除当前登录用户'
  }
  if (user.username === 'admin') {
    return 'admin 是系统超级管理员账户，不可删除'
  }
  return `删除用户 ${user.username}`
}

async function deleteUser(user) {
  if (user.username === username.value) {
    ElMessage.warning('不能删除当前登录用户')
    return
  }
  
  if (user.username === 'admin') {
    ElMessage({
      message: 'admin 是系统超级管理员账户，为确保系统安全不可删除',
      type: 'warning',
      duration: 3000,
      showClose: true
    })
    return
  }
  
  try {
    await ElMessageBox.confirm(`确定要删除用户 "${user.username}" 吗？`, '确认删除', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })
    
    await axios.delete(`/api/users/${user.id}`)
    ElMessage.success('用户删除成功')
    loadUsers()
  } catch (e) {
    if (e !== 'cancel') {
      ElMessage.error(e.response?.data?.error || '删除失败')
    }
  }
}

// 批量操作相关方法
function handleSelectionChange(selection) {
  selectedUsers.value = selection
}

function clearSelection() {
  selectedUsers.value = []
}

async function batchToggleUsers(action) {
  if (selectedUsers.value.length === 0) {
    ElMessage.warning('请先选择要操作的用户')
    return
  }

  // 检查是否包含admin用户
  const hasAdmin = selectedUsers.value.some(user => user.username === 'admin')
  if (action === 'disable' && hasAdmin) {
    ElMessage.warning('不能禁用超级管理员账号')
    return
  }

  const actionText = action === 'enable' ? '启用' : '禁用'
  const usernames = selectedUsers.value.map(user => user.username).join(', ')
  
  try {
    await ElMessageBox.confirm(
      `确定要${actionText}以下用户吗？\n${usernames}`,
      `确认批量${actionText}`,
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    batchActionLoading.value = true
    
    const userIds = selectedUsers.value.map(user => user.id)
    await axios.post('/api/users/batch-toggle', {
      user_ids: userIds,
      action: action
    })

    ElMessage.success(`成功${actionText}了 ${selectedUsers.value.length} 个用户`)
    clearSelection()
    loadUsers()
  } catch (e) {
    if (e !== 'cancel') {
      ElMessage.error(e.response?.data?.error || `批量${actionText}失败`)
    }
  } finally {
    batchActionLoading.value = false
  }
}

function formatDate(dateString) {
  if (!dateString) return ''

  // 如果是数字（Unix时间戳），需要判断是秒还是毫秒
  if (typeof dateString === 'number') {
    // 如果是秒时间戳（小于10位数字），转换为毫秒
    const timestamp = dateString < 10000000000 ? dateString * 1000 : dateString
    const date = new Date(timestamp)
    return date.toLocaleString('zh-CN')
  }

  // 如果是字符串，直接解析
  const date = new Date(dateString)
  return date.toLocaleString('zh-CN')
}

// IP地址格式化函数
function formatIpAddress(ip) {
  if (!ip) return '-'

  // IPv6本地回环地址转换
  if (ip === '::1') {
    return '127.0.0.1'
  }

  // IPv6地址转换为IPv4（如果可能）
  if (ip.includes('::ffff:')) {
    const ipv4Part = ip.split('::ffff:')[1]
    if (ipv4Part && /^\d+\.\d+\.\d+\.\d+$/.test(ipv4Part)) {
      return ipv4Part
    }
  }

  // 如果是IPv4地址，直接返回
  if (/^\d+\.\d+\.\d+\.\d+$/.test(ip)) {
    return ip
  }

  // 其他IPv6地址显示为简化格式
  if (ip.includes(':')) {
    return `IPv6:${ip.substring(0, 8)}...`
  }

  return ip
}

// 最后活跃时间格式化函数
function formatLastPing(lastPing) {
  if (!lastPing) return '-'

  const now = Date.now()
  const diff = now - lastPing

  if (diff < 1000) {
    return '刚刚'
  } else if (diff < 60000) {
    return `${Math.floor(diff / 1000)}秒前`
  } else if (diff < 3600000) {
    return `${Math.floor(diff / 60000)}分钟前`
  } else if (diff < 86400000) {
    return `${Math.floor(diff / 3600000)}小时前`
  } else {
    return `${Math.floor(diff / 86400000)}天前`
  }
}

// 紧凑日期格式化 - 用于遗嘱消息表格
function formatCompactDate(dateString) {
  if (!dateString) return ''
  const date = new Date(dateString)
  const now = new Date()
  const today = new Date(now.getFullYear(), now.getMonth(), now.getDate())
  const logDate = new Date(date.getFullYear(), date.getMonth(), date.getDate())
  
  // 如果是今天，显示今天+时间
  if (logDate.getTime() === today.getTime()) {
    return '今天 ' + date.toLocaleString('zh-CN', {
      hour: '2-digit',
      minute: '2-digit'
    })
  }
  
  // 如果是昨天，显示昨天+时间
  const yesterday = new Date(today.getTime() - 24 * 60 * 60 * 1000)
  if (logDate.getTime() === yesterday.getTime()) {
    return '昨天 ' + date.toLocaleString('zh-CN', {
      hour: '2-digit',
      minute: '2-digit'
    })
  }
  
  // 其他情况显示完整的年月日时间，格式：MM-DD HH:mm
  return date.toLocaleString('zh-CN', {
    year: '2-digit',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit'
  }).replace(/\//g, '-').replace(/\s/, ' ')
}



// MQTT管理函数 - 初始加载，后续通过SSE实时更新
async function loadMqttStats() {
  try {
    const res = await axios.get('/api/mqtt/stats')
    const newStats = res.data.stats

    // 智能更新：只更新非编辑状态的数据
    updateMqttStatsSelectively(newStats)

    // 更新处理后的主题列表
    updateProcessedTopics()
  } catch (e) {
    debugError('加载MQTT状态失败:', e)
    ElMessage.error('加载MQTT状态失败')
  }
}

// 智能更新MQTT统计数据
function updateMqttStatsSelectively(newStats) {
  debugLog('🔄 开始智能更新MQTT数据')

  // 1. 总是更新实时数据（这些不会被用户编辑）
  mqttStats.value.port = newStats.port
  mqttStats.value.running = newStats.running
  mqttStats.value.clients = newStats.clients
  mqttStats.value.topics = newStats.topics

  // 2. 智能更新列表数据（Vue会自动diff）
  updateClientList(newStats.client_list || [])
  updateTopicList(newStats.topic_list || [])
  updateBlacklist(newStats.blacklist || [])
  updateFailedAuth(newStats.failed_auth || [])

  // 3. 智能更新配置（跳过正在编辑的字段）
  updateConfigIntelligently(newStats.config || {})
}

// 智能更新客户端列表
function updateClientList(newClientList) {
  // Vue的响应式系统会自动处理列表的增删改
  mqttStats.value.client_list = newClientList
  debugLog('📋 客户端列表已更新，当前数量:', newClientList.length)
}

// 智能更新主题列表
function updateTopicList(newTopicList) {
  mqttStats.value.topic_list = newTopicList
  debugLog('📋 主题列表已更新，当前数量:', newTopicList.length)
}

// 智能更新黑名单
function updateBlacklist(newBlacklist) {
  mqttStats.value.blacklist = newBlacklist
  debugLog('📋 黑名单已更新，当前数量:', newBlacklist.length)
}

// 智能更新失败认证记录
function updateFailedAuth(newFailedAuth) {
  mqttStats.value.failed_auth = newFailedAuth
  debugLog('📋 失败认证记录已更新，当前数量:', newFailedAuth.length)
}

// 智能更新配置（跳过正在编辑的字段）
function updateConfigIntelligently(newConfig) {
  const configFields = [
    'require_auth', 'allow_anonymous', 'max_failed_attempts',
    'blacklist_duration', 'cleanup_interval', 'heartbeat_interval',
    'heartbeat_timeout', 'heartbeat_packet_interval'
  ]

  let updatedFields = []
  let skippedFields = []

  configFields.forEach(field => {
    if (newConfig.hasOwnProperty(field)) {
      if (!isFieldBeingEdited(field)) {
        // 更新服务器配置和表单配置
        mqttStats.value.config[field] = newConfig[field]
        mqttConfig[field] = newConfig[field]
        updatedFields.push(field)
      } else {
        skippedFields.push(field)
      }
    }
  })

  if (updatedFields.length > 0) {
    debugLog('✅ 配置字段已更新:', updatedFields)
  }
  if (skippedFields.length > 0) {
    debugLog('⏸️ 跳过正在编辑的字段:', skippedFields)
  }
}

async function publishMessage() {
  if (!publishForm.topic || !publishForm.message) {
    ElMessage.error('请输入主题和消息内容')
    return
  }
  
  publishLoading.value = true
  try {
    await axios.post('/api/mqtt/publish', {
      topic: publishForm.topic,
      message: publishForm.message,
      qos: publishForm.qos,
      retain: publishForm.retain
    })
    ElMessage.success('消息发布成功')
    // 刷新统计信息
    loadMqttStats()
  } catch (e) {
    ElMessage.error(e.response?.data?.error || '消息发布失败')
  }
  publishLoading.value = false
}

async function sendToClient() {
  if (!sendForm.clientId || !sendForm.topic || !sendForm.message) {
    ElMessage.error('请选择客户端并输入主题和消息内容')
    return
  }
  
  // 确保客户端ID是字符串
  const clientId = typeof sendForm.clientId === 'object' ? sendForm.clientId.id : sendForm.clientId

  const payload = {
    client_id: String(clientId),
    topic: String(sendForm.topic),
    message: String(sendForm.message),
    qos: sendForm.qos,
    retain: sendForm.retain
  }

  sendLoading.value = true
  try {
    await axios.post('/api/mqtt/send', payload)
    ElMessage.success(`消息已发送到客户端 ${sendForm.clientId}`)
    // 刷新统计信息
    loadMqttStats()
  } catch (e) {
    ElMessage.error(e.response?.data?.error || '消息发送失败')
  }
  sendLoading.value = false
}

// 客户端选择变化处理
function onClientChange() {
  // 清空之前选择的主题
  sendForm.topic = ''
  selectedClientTopics.value = []

  if (!sendForm.clientId) {
    return
  }

  // 查找选中客户端的订阅主题
  const selectedClient = mqttStats.value.client_list?.find(client => client.id === sendForm.clientId)

  if (selectedClient && selectedClient.subscribed_topics && Array.isArray(selectedClient.subscribed_topics)) {
    selectedClientTopics.value = [...selectedClient.subscribed_topics]
  }
}

// 调试客户端列表
function debugClientList() {
  console.log('=== MQTT客户端调试信息 ===')
  console.log('客户端总数:', mqttStats.value.clients)
  console.log('客户端列表长度:', mqttStats.value.client_list?.length || 0)
  console.log('客户端列表详情:', mqttStats.value.client_list)

  // 详细检查每个客户端的订阅主题
  if (mqttStats.value.client_list) {
    mqttStats.value.client_list.forEach((client, index) => {
      console.log(`客户端 ${index + 1}:`, {
        id: client.id,
        username: client.username,
        subscribed_topics: client.subscribed_topics,
        topics_count: client.topics,
        topics_type: typeof client.subscribed_topics,
        topics_is_array: Array.isArray(client.subscribed_topics)
      })
    })
  }

  console.log('主题总数:', mqttStats.value.topics)
  console.log('主题列表:', mqttStats.value.topic_list)
  console.log('选中客户端主题:', selectedClientTopics.value)
  console.log('========================')

  ElMessage.info(`当前有 ${mqttStats.value.client_list?.length || 0} 个客户端连接，详情请查看控制台`)
}

// 黑名单管理函数
function showAddBlacklistDialog() {
  blacklistForm.ip = ''
  blacklistForm.username = ''
  blacklistForm.reason = ''
  blacklistForm.hours = 24
  blacklistDialogVisible.value = true
}

async function addToBlacklist() {
  if (!blacklistForm.ip || !blacklistForm.reason || !blacklistForm.hours) {
    ElMessage.error('请填写完整的黑名单信息')
    return
  }
  
  try {
    await axios.post('/api/mqtt/blacklist', {
      ip: blacklistForm.ip,
      username: blacklistForm.username,
      reason: blacklistForm.reason,
      hours: blacklistForm.hours
    })
    ElMessage.success('黑名单添加成功')
    blacklistDialogVisible.value = false
    loadMqttStats() // 刷新状态
  } catch (e) {
    ElMessage.error(e.response?.data?.error || '添加黑名单失败')
  }
}

async function removeFromBlacklist(ip) {
  try {
    await ElMessageBox.confirm(`确定要移除黑名单 ${ip} 吗？`, '确认移除', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })
    
    await axios.delete(`/api/mqtt/blacklist/${ip}`)
    ElMessage.success('黑名单移除成功')
    loadMqttStats() // 刷新状态
  } catch (e) {
    if (e !== 'cancel') {
      ElMessage.error(e.response?.data?.error || '移除黑名单失败')
    }
  }
}

async function clearFailedAuth(ip) {
  try {
    await ElMessageBox.confirm(`确定要清除 ${ip} 的失败认证记录吗？`, '确认清除', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })
    
    await axios.delete(`/api/mqtt/failedauth/${ip}`)
    ElMessage.success('失败认证记录清除成功')
    loadMqttStats() // 刷新状态
  } catch (e) {
    if (e !== 'cancel') {
      ElMessage.error(e.response?.data?.error || '清除失败认证记录失败')
    }
  }
}

// 配置管理函数
async function updateMqttConfig() {
  configLoading.value = true
  try {
    await axios.put('/api/mqtt/config', mqttConfig)
    ElMessage.success('配置更新成功')
    loadMqttStats() // 刷新状态
  } catch (e) {
    ElMessage.error(e.response?.data?.error || '配置更新失败')
  }
  configLoading.value = false
}

// 保持消息管理函数
async function loadRetainedMessages() {
  retainedLoading.value = true
  try {
    const response = await axios.get('/api/mqtt/retained')
    retainedMessages.value = response.data.messages || []
  } catch (e) {
    ElMessage.error(e.response?.data?.error || '加载保持消息失败')
    retainedMessages.value = []
  }
  retainedLoading.value = false
}

async function deleteRetainedMessage(topic) {
  try {
    await ElMessageBox.confirm(`确定要删除主题 "${topic}" 的保持消息吗？`, '确认删除', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })
    
    deleteRetainedLoading.value[topic] = true
    await axios.delete('/api/mqtt/retained', {
      data: { topic }
    })
    ElMessage.success('保持消息删除成功')
    loadRetainedMessages() // 刷新列表
  } catch (e) {
    if (e !== 'cancel') {
      ElMessage.error(e.response?.data?.error || '删除保持消息失败')
    }
  } finally {
    delete deleteRetainedLoading.value[topic]
  }
}

async function clearAllRetainedMessages() {
  try {
    await ElMessageBox.confirm(
      '⚠️ 警告：这将删除所有保持消息，此操作不可恢复！\n确定要继续吗？', 
      '确认清空', 
      {
        confirmButtonText: '确定清空',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    clearRetainedLoading.value = true
    await axios.delete('/api/mqtt/retained/all')
    ElMessage.success('所有保持消息已清空')
    retainedMessages.value = []
  } catch (e) {
    if (e !== 'cancel') {
      ElMessage.error(e.response?.data?.error || '清空保持消息失败')
    }
  }
  clearRetainedLoading.value = false
}

// 遗嘱消息管理函数
async function loadWillMessages() {
  willLoading.value = true
  try {
    // 先刷新MQTT状态获取最新的客户端列表
    await loadMqttStats()
    
    const response = await axios.get('/api/mqtt/will')
    willMessages.value = response.data.messages || []
    
    // 检查每个遗嘱消息对应的客户端是否在线
    if (mqttStats.value.client_list) {
      const onlineClientIds = new Set(mqttStats.value.client_list.map(client => client.id))
      willMessages.value = willMessages.value.map(will => ({
        ...will,
        is_connected: onlineClientIds.has(will.client_id)
      }))
    }
  } catch (e) {
    ElMessage.error(e.response?.data?.error || '加载遗嘱消息失败')
    willMessages.value = []
  }
  willLoading.value = false
}

async function deleteWillMessage(clientId) {
  try {
    await ElMessageBox.confirm(`确定要清除客户端 "${clientId}" 的遗嘱消息吗？`, '确认清除', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })
    
    deleteWillLoading.value[clientId] = true
    await axios.delete('/api/mqtt/will', {
      data: { client_id: clientId }
    })
    ElMessage.success('遗嘱消息清除成功')
    loadWillMessages() // 刷新列表
  } catch (e) {
    if (e !== 'cancel') {
      ElMessage.error(e.response?.data?.error || '清除遗嘱消息失败')
    }
  } finally {
    delete deleteWillLoading.value[clientId]
  }
}

// 操作日志管理函数
async function loadLogs() {
  logsLoading.value = true
  try {
    const params = new URLSearchParams({
      page: logPagination.page,
      page_size: logPagination.page_size,
      ...logFilters
    })
    
    const response = await axios.get(`/api/logs?${params}`)
    logs.value = response.data.logs || []
    logPagination.total_count = response.data.total || 0
    logPagination.total_pages = response.data.total_pages || 1
  } catch (e) {
    ElMessage.error(e.response?.data?.error || '加载日志失败')
    logs.value = []
  }
  logsLoading.value = false
}

async function loadLogStats() {
  try {
    const response = await axios.get('/api/logs/stats')
    logStats.value = response.data.stats || {}
  } catch (e) {
    console.error('Failed to load log stats:', e)
  }
}

async function refreshLogs() {
  await loadLogs()
  await loadLogStats()
  ElMessage.success('日志已刷新')
}

async function clearLogs() {
  try {
    // 弹出选择清理范围的对话框
    const { value: clearOption } = await ElMessageBox.prompt(
      '请选择要清理的日志范围：\n' +
      '• 1 = 清理1天前的日志（清理几乎所有日志）\n' +
      '• 7 = 清理7天前的日志\n' +
      '• 30 = 清理30天前的日志（默认）\n' +
      '• 90 = 清理90天前的日志\n' +
      '• 365 = 清理1年前的日志',
      '选择清理范围',
      {
        confirmButtonText: '开始清理',
        cancelButtonText: '取消',
        inputPlaceholder: '请输入天数 (1-365)',
        inputValue: '30',
        inputValidator: (value) => {
          const days = parseInt(value)
          if (isNaN(days) || days < 1 || days > 365) {
            return '请输入1-365之间的数字'
          }
          return true
        },
        type: 'warning'
      }
    )
    
    const days = parseInt(clearOption)
    
    // 二次确认
    let confirmMessage = ''
    if (days <= 7) {
      confirmMessage = `⚠️ 警告：这将删除${days}天前的所有日志，几乎等于清空日志！\n确定要继续吗？`
    } else {
      confirmMessage = `确定要清理${days}天前的操作日志吗？`
    }
    
    await ElMessageBox.confirm(confirmMessage, '最终确认', {
      confirmButtonText: '确定清理',
      cancelButtonText: '取消',
      type: 'warning',
      dangerouslyUseHTMLString: false
    })
    
    const response = await axios.delete(`/api/logs/clear?days=${days}`)
    
    if (response.data.rows_affected > 0) {
      ElMessage.success(`成功清理了 ${response.data.rows_affected} 条${days}天前的日志`)
    } else {
      ElMessage.info(`没有找到${days}天前的日志，无需清理`)
    }
    
    await loadLogs()
    await loadLogStats()
  } catch (e) {
    if (e !== 'cancel') {
      ElMessage.error(e.response?.data?.error || '清理日志失败')
    }
  }
}

async function filterLogs() {
  logPagination.page = 1
  await loadLogs()
}

// 快捷清理日志功能
async function quickClearLogs(days) {
  try {
    let confirmMessage = ''
    if (days <= 7) {
      confirmMessage = `⚠️ 警告：这将删除${days}天前的所有日志，几乎等于清空日志！\n确定要继续吗？`
    } else {
      confirmMessage = `确定要清理${days}天前的操作日志吗？`
    }
    
    await ElMessageBox.confirm(confirmMessage, '确认清理', {
      confirmButtonText: '确定清理',
      cancelButtonText: '取消',
      type: 'warning'
    })
    
    const response = await axios.delete(`/api/logs/clear?days=${days}`)
    
    if (response.data.rows_affected > 0) {
      ElMessage.success(`成功清理了 ${response.data.rows_affected} 条${days}天前的日志`)
    } else {
      ElMessage.info(`没有找到${days}天前的日志，无需清理`)
    }
    
    await loadLogs()
    await loadLogStats()
  } catch (e) {
    if (e !== 'cancel') {
      ElMessage.error(e.response?.data?.error || '清理日志失败')
    }
  }
}

function formatTime(timeStr) {
  if (!timeStr) return ''
  const date = new Date(timeStr)
  const now = new Date()
  const today = new Date(now.getFullYear(), now.getMonth(), now.getDate())
  const logDate = new Date(date.getFullYear(), date.getMonth(), date.getDate())
  
  // 如果是今天，只显示时间
  if (logDate.getTime() === today.getTime()) {
    return date.toLocaleString('zh-CN', {
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit'
    })
  }
  
  // 如果是昨天，显示"昨天 HH:mm"
  const yesterday = new Date(today.getTime() - 24 * 60 * 60 * 1000)
  if (logDate.getTime() === yesterday.getTime()) {
    return '昨天 ' + date.toLocaleString('zh-CN', {
      hour: '2-digit',
      minute: '2-digit'
    })
  }
  
  // 其他情况显示完整日期时间，但使用更紧凑的格式
  return date.toLocaleString('zh-CN', {
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit'
  })
}

function formatOperationType(type) {
  const typeMap = {
    'LOGIN': '登录',
    'LOGIN_FAILED': '登录失败',
    'CREATE_USER': '创建用户',
    'CREATE_USER_FAILED': '创建用户失败',
    'UPDATE_USER': '更新用户',
    'UPDATE_USER_FAILED': '更新用户失败',
    'DELETE_USER': '删除用户',
    'DELETE_USER_FAILED': '删除用户失败',
    'CLEAR_LOGS': '清理日志',
    'CLEAR_LOGS_FAILED': '清理日志失败'
  }
  return typeMap[type] || type
}

function getOperationTypeClass(type) {
  if (type.includes('FAILED')) {
    return 'operation-failed'
  }
  if (type.includes('LOGIN')) {
    return 'operation-login'
  }
  if (type.includes('CREATE') || type.includes('UPDATE') || type.includes('DELETE')) {
    return 'operation-manage'
  }
  return 'operation-other'
}

// 日志翻页处理函数
function handleLogPageChange(page) {
  logPagination.page = page
  loadLogs()
}

function handleLogPageSizeChange(size) {
  logPagination.page_size = size
  logPagination.page = 1 // 重置到第一页
  loadLogs()
}

// 系统设置管理函数
async function loadSecurityConfig(category = 'security', retryCount = 0) {
  try {
    const response = await axios.get(`/api/security/config?category=${category}`)
    const configs = response.data.configs || []

    // 根据分类更新对应的配置对象
    configs.forEach(config => {
      const key = config.key_name
      let value = config.value

      // 根据类型转换值
      if (config.value_type === 'bool') {
        value = value === 'true'
      } else if (config.value_type === 'int') {
        value = parseInt(value)
      }

      // 调试：特别关注cors_dev_mode的加载
      if (key === 'cors_dev_mode') {
        console.log('🔍 加载cors_dev_mode配置:', {
          key,
          originalValue: config.value,
          convertedValue: value,
          valueType: config.value_type
        })
      }

      // 更新对应的配置对象
      if (category === 'security' && securityConfig.hasOwnProperty(key)) {
        const oldValue = securityConfig[key]
        securityConfig[key] = value

        // 调试：特别关注cors_dev_mode的更新
        if (key === 'cors_dev_mode') {
          console.log('✅ cors_dev_mode已更新:', {
            oldValue,
            newValue: value,
            currentSecurityConfig: { ...securityConfig }
          })
        }
      } else if (category === 'rate_limit' && rateLimitConfig.hasOwnProperty(key)) {
        rateLimitConfig[key] = value
      } else if (category === 'database' && databaseConfig.hasOwnProperty(key)) {
        databaseConfig[key] = value
      } else if (category === 'validation' && validationConfig.hasOwnProperty(key)) {
        validationConfig[key] = value
      } else if (category === 'captcha' && captchaConfig.hasOwnProperty(key)) {
        captchaConfig[key] = value
      }
    })

    debugLog(`✅ 加载${category}配置成功:`, configs)
  } catch (error) {
    // 如果是频率限制错误且重试次数少于3次，则重试
    if (error.response?.status === 429 && retryCount < 3) {
      console.warn(`Rate limit hit for ${category}, retrying in ${(retryCount + 1) * 500}ms...`)
      await new Promise(resolve => setTimeout(resolve, (retryCount + 1) * 500))
      return loadSecurityConfig(category, retryCount + 1)
    }

    console.error(`Failed to load ${category} config:`, error)
    if (error.response?.status !== 429) {
      ElMessage.error(`加载${category}配置失败: ${error.response?.data?.error || error.message}`)
    }
  }
}

async function saveSecurityConfig() {
  configSaving.value = true
  try {
    // 准备要保存的配置数据
    const configsToSave = []

    // 根据当前活动的标签页确定要保存的配置
    if (activeSettingsTab.value === 'cors') {
      console.log('🔍 准备保存CORS配置，当前securityConfig:', { ...securityConfig })

      Object.keys(securityConfig).forEach(key => {
        const configItem = {
          category: 'security',
          key: key,
          value: String(securityConfig[key])
        }

        // 特别关注cors_dev_mode
        if (key === 'cors_dev_mode') {
          console.log('🔍 保存cors_dev_mode配置:', {
            key,
            originalValue: securityConfig[key],
            stringValue: String(securityConfig[key])
          })
        }

        configsToSave.push(configItem)
      })

      console.log('🔍 即将保存的CORS配置:', configsToSave)
    } else if (activeSettingsTab.value === 'rate-limit') {
      Object.keys(rateLimitConfig).forEach(key => {
        configsToSave.push({
          category: 'rate_limit',
          key: key,
          value: String(rateLimitConfig[key])
        })
      })
    } else if (activeSettingsTab.value === 'database') {
      Object.keys(databaseConfig).forEach(key => {
        configsToSave.push({
          category: 'database',
          key: key,
          value: String(databaseConfig[key])
        })
      })
    } else if (activeSettingsTab.value === 'validation') {
      Object.keys(validationConfig).forEach(key => {
        configsToSave.push({
          category: 'validation',
          key: key,
          value: String(validationConfig[key])
        })
      })
    } else if (activeSettingsTab.value === 'captcha') {
      Object.keys(captchaConfig).forEach(key => {
        configsToSave.push({
          category: 'captcha',
          key: key,
          value: String(captchaConfig[key])
        })
      })
    }

    // 发送保存请求
    await axios.put('/api/security/config', {
      configs: configsToSave
    })

    ElMessage.success('安全配置保存成功')
    debugLog('✅ 安全配置保存成功:', configsToSave)
  } catch (error) {
    console.error('Failed to save security config:', error)
    ElMessage.error('保存安全配置失败: ' + (error.response?.data?.error || error.message))
  } finally {
    configSaving.value = false
  }
}

async function reloadConfig() {
  configSaving.value = true
  try {
    await axios.post('/api/security/config/reload')
    ElMessage.success('配置重载成功，新配置已生效')
    debugLog('✅ 配置重载成功')
  } catch (error) {
    console.error('Failed to reload config:', error)
    ElMessage.error('配置重载失败: ' + (error.response?.data?.error || error.message))
  } finally {
    configSaving.value = false
  }
}

// CORS配置辅助函数
function addCurrentOrigin() {
  const currentOrigin = window.location.origin
  const currentOrigins = securityConfig.cors_allowed_origins.split(',').map(o => o.trim()).filter(o => o)

  if (!currentOrigins.includes(currentOrigin)) {
    currentOrigins.push(currentOrigin)
    securityConfig.cors_allowed_origins = currentOrigins.join(',')
    ElMessage.success(`已添加当前访问地址: ${currentOrigin}`)
  } else {
    ElMessage.info('当前访问地址已在允许列表中')
  }
}

function addCommonOrigins() {
  const commonOrigins = [
    'http://localhost:5173',
    'http://127.0.0.1:5173',
    'http://***********:5173',
    'http://***********:5173',
    'http://********:5173'
  ]

  const currentOrigins = securityConfig.cors_allowed_origins.split(',').map(o => o.trim()).filter(o => o)
  let addedCount = 0

  commonOrigins.forEach(origin => {
    if (!currentOrigins.includes(origin)) {
      currentOrigins.push(origin)
      addedCount++
    }
  })

  if (addedCount > 0) {
    securityConfig.cors_allowed_origins = currentOrigins.join(',')
    ElMessage.success(`已添加 ${addedCount} 个常用地址`)
  } else {
    ElMessage.info('所有常用地址已在允许列表中')
  }
}

function resetToDefaults() {
  securityConfig.cors_allowed_origins = 'http://localhost:5173,http://127.0.0.1:5173,http://***********:5173'
  securityConfig.cors_allowed_methods = 'GET,POST,PUT,DELETE,OPTIONS'
  securityConfig.cors_allowed_headers = 'Origin,Content-Type,Authorization'
  securityConfig.cors_allow_credentials = true
  securityConfig.cors_max_age = 86400
  securityConfig.cors_dev_mode = false
  ElMessage.success('已重置为默认CORS配置')
}

// 验证码配置管理函数
async function loadCaptchaConfig() {
  try {
    await loadSecurityConfig('captcha')
    ElMessage.success('验证码配置加载成功')
  } catch (error) {
    console.error('Failed to load captcha config:', error)
    ElMessage.error('加载验证码配置失败: ' + (error.response?.data?.error || error.message))
  }
}

async function saveCaptchaConfig() {
  configSaving.value = true
  try {
    const configsToSave = []

    // 准备验证码配置数据
    Object.keys(captchaConfig).forEach(key => {
      configsToSave.push({
        category: 'captcha',
        key: key,
        value: String(captchaConfig[key])
      })
    })

    // 发送保存请求
    await axios.put('/api/security/config', {
      configs: configsToSave
    })

    ElMessage.success('验证码配置保存成功')
    debugLog('✅ 验证码配置保存成功:', configsToSave)

    // 如果调试输出被禁用，显示安全提示
    if (!captchaConfig.debug_output) {
      ElMessage({
        message: '验证码调试输出已禁用，提高了系统安全性',
        type: 'success',
        duration: 3000
      })
    } else {
      ElMessage({
        message: '⚠️ 验证码调试输出已启用，生产环境建议禁用',
        type: 'warning',
        duration: 5000
      })
    }
  } catch (error) {
    console.error('Failed to save captcha config:', error)
    ElMessage.error('保存验证码配置失败: ' + (error.response?.data?.error || error.message))
  } finally {
    configSaving.value = false
  }
}

// 加载调试配置
async function loadDebugConfig() {
  try {
    const response = await axios.get('/api/config/system')
    const configs = response.data.configs || []

    configs.forEach(config => {
      if (config.key_name === 'debug_mode') {
        debugConfig.debug_mode = config.value === 'true'
      } else if (config.key_name === 'frontend_debug_logs') {
        debugConfig.frontend_debug_logs = config.value === 'true'
      }
    })

    debugLog('✅ 调试配置加载成功:', debugConfig)
  } catch (error) {
    console.error('Failed to load debug config:', error)
    ElMessage.error('加载调试配置失败: ' + (error.response?.data?.error || error.message))
  }
}

// 保存调试配置
async function saveDebugConfig() {
  configSaving.value = true
  try {
    const configsToSave = []
    Object.keys(debugConfig).forEach(key => {
      configsToSave.push({
        category: 'system',
        key: key,
        value: String(debugConfig[key]),
        value_type: 'bool',
        description: key === 'debug_mode' ? '调试模式（禁用前端缓存，开发调试用）' : '启用前端调试日志输出（开发调试用）'
      })
    })

    await axios.put('/api/config', {
      configs: configsToSave
    })

    ElMessage.success('调试配置保存成功')
    debugLog('✅ 调试配置保存成功:', configsToSave)

    // 如果启用了调试模式，提示用户刷新页面
    if (debugConfig.debug_mode) {
      ElMessage.info('调试模式已启用，建议刷新页面以应用缓存设置')
    }
  } catch (error) {
    console.error('Failed to save debug config:', error)
    ElMessage.error('保存调试配置失败: ' + (error.response?.data?.error || error.message))
  } finally {
    configSaving.value = false
  }
}

// RSA证书管理函数
async function loadRSACertificateInfo() {
  rsaCertLoading.value = true
  try {
    debugLog('🔄 开始加载RSA证书信息...')
    const response = await axios.get('/api/certificates/rsa-info')
    rsaCertInfo.value = response.data
    debugLog('✅ RSA证书信息加载成功:', rsaCertInfo.value)
  } catch (error) {
    console.error('Failed to load RSA certificate info:', error)

    // 如果是认证错误，显示提示
    if (error.response?.status === 401) {
      console.warn('RSA证书信息加载失败：认证错误')
    } else if (error.response?.status === 404) {
      console.warn('RSA证书信息加载失败：RSA密钥对未初始化')
    } else {
      console.warn('RSA证书信息加载失败：', error.message)
    }
  } finally {
    rsaCertLoading.value = false
  }
}

async function generateRSACertificate() {
  rsaCertGenerating.value = true
  try {
    const response = await axios.post('/api/certificates/generate-rsa', rsaCertForm)

    ElMessage.success('RSA证书生成成功')
    debugLog('✅ RSA证书生成成功:', response.data)

    // 关闭对话框
    showRSACertDialog.value = false

    // 重新加载RSA证书信息
    await loadRSACertificateInfo()

    // 显示成功信息
    ElMessage({
      message: `RSA证书已生成，有效期${rsaCertForm.validity_period.label}`,
      type: 'success',
      duration: 5000
    })
  } catch (error) {
    console.error('Failed to generate RSA certificate:', error)
    ElMessage.error('生成RSA证书失败: ' + (error.response?.data?.error || error.message))
  } finally {
    rsaCertGenerating.value = false
  }
}

// DNS名称管理
function removeDNSName(dns) {
  const index = rsaCertForm.dns_names.indexOf(dns)
  if (index > -1) {
    rsaCertForm.dns_names.splice(index, 1)
  }
}

function showDNSInput() {
  inputDNSVisible.value = true
  nextTick(() => {
    inputDNSRef.value?.focus()
  })
}

function handleDNSInputConfirm() {
  if (inputDNSValue.value && !rsaCertForm.dns_names.includes(inputDNSValue.value)) {
    rsaCertForm.dns_names.push(inputDNSValue.value)
  }
  inputDNSVisible.value = false
  inputDNSValue.value = ''
}

// IP地址管理
function removeIPAddress(ip) {
  const index = rsaCertForm.ip_addresses.indexOf(ip)
  if (index > -1) {
    rsaCertForm.ip_addresses.splice(index, 1)
  }
}

function showIPInput() {
  inputIPVisible.value = true
  nextTick(() => {
    inputIPRef.value?.focus()
  })
}

function handleIPInputConfirm() {
  if (inputIPValue.value && !rsaCertForm.ip_addresses.includes(inputIPValue.value)) {
    // 简单的IP地址验证
    const ipRegex = /^(\d{1,3}\.){3}\d{1,3}$/
    if (ipRegex.test(inputIPValue.value)) {
      rsaCertForm.ip_addresses.push(inputIPValue.value)
    } else {
      ElMessage.warning('请输入有效的IP地址')
      return
    }
  }
  inputIPVisible.value = false
  inputIPValue.value = ''
}

// 证书下载和删除功能
async function downloadCertificate(cert) {
  try {
    // 获取证书文件
    const response = await axios.get(`/api/certificates/download/${cert.name}`, {
      responseType: 'blob'
    })

    // 创建下载链接
    const url = window.URL.createObjectURL(new Blob([response.data]))
    const link = document.createElement('a')
    link.href = url
    link.setAttribute('download', cert.name)
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)

    ElMessage.success('证书下载成功')
  } catch (error) {
    console.error('Failed to download certificate:', error)
    ElMessage.error('证书下载失败: ' + (error.response?.data?.error || error.message))
  }
}

async function deleteCertificate(cert) {
  try {
    // 如果是当前使用的证书或默认证书，不允许删除
    if (cert.is_currently_used) {
      ElMessage.warning('当前正在使用的证书不能删除')
      return
    }

    if (cert.is_default_cert) {
      ElMessage.warning('默认系统证书不能删除')
      return
    }

    // 确认删除
    await ElMessageBox.confirm(
      `确定要删除证书 "${cert.name}" 吗？此操作不可恢复。`,
      '删除确认',
      {
        confirmButtonText: '确定删除',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    // 删除证书
    await axios.delete(`/api/certificates/delete/${cert.name}`)

    // 重新加载证书信息
    await loadRSACertificateInfo()

    ElMessage.success('证书删除成功')
  } catch (error) {
    if (error !== 'cancel') {
      console.error('Failed to delete certificate:', error)
      ElMessage.error('证书删除失败: ' + (error.response?.data?.error || error.message))
    }
  }
}

// 切换使用证书
// 获取当前使用的证书描述
function getCurrentCertificateDescription() {
  // 查找当前使用的证书
  const currentCert = rsaCertificateList.value.find(cert => cert.is_currently_used)

  if (currentCert) {
    if (currentCert.is_default_cert) {
      return `系统当前使用默认证书 (${currentCert.name}) 进行加密和认证操作。`
    } else {
      return `系统当前使用证书 ${currentCert.name} 进行加密和认证操作。`
    }
  } else {
    return '系统当前使用默认RSA密钥对进行加密和认证操作。'
  }
}

// 获取切换按钮的提示文本
function getSwitchButtonTooltip(cert) {
  return cert.is_currently_used ? '当前使用中' : '切换使用'
}



async function switchToCertificate(cert) {
  try {
    // 如果已经是当前使用的证书，不需要切换
    if (cert.is_currently_used) {
      ElMessage.info('已经在使用该证书')
      return
    }

    // 确认切换
    await ElMessageBox.confirm(
      `确定要将系统切换到使用证书 "${cert.name}" 吗？\n\n` +
      `切换后，系统将立即使用该证书进行加密和认证操作。\n` +
      `此操作会自动备份当前的系统密钥文件。`,
      '切换证书确认',
      {
        confirmButtonText: '确定切换',
        cancelButtonText: '取消',
        type: 'warning',
        dangerouslyUseHTMLString: true
      }
    )

    // 切换证书
    const response = await axios.post(`/api/certificates/switch-to/${cert.name}`)

    // 重新加载证书信息
    await loadRSACertificateInfo()

    ElMessage({
      message: '证书切换成功，系统现在使用新的证书进行加密和认证',
      type: 'success',
      duration: 5000
    })

    debugLog('✅ 证书切换成功:', response.data)
  } catch (error) {
    if (error !== 'cancel') {
      console.error('Failed to switch certificate:', error)
      ElMessage.error('证书切换失败: ' + (error.response?.data?.error || error.message))
    }
  }
}





function formatFileSize(bytes) {
  if (bytes === 0) return '0 B'
  const k = 1024
  const sizes = ['B', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}

function formatDateTime(dateString) {
  if (!dateString) return '-'
  const date = new Date(dateString)
  return date.toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit'
  })
}

// 移动端相关函数
function checkMobile() {
  isMobile.value = window.innerWidth <= 768
}

function toggleSidebar() {
  sidebarOpen.value = !sidebarOpen.value
}

function closeSidebar() {
  sidebarOpen.value = false
}

// 监听窗口大小变化
function handleResize() {
  checkMobile()
  if (!isMobile.value) {
    sidebarOpen.value = false
  }
}

// 监听MQTT标签切换
watch(mqttActiveTab, (newTab) => {
  if (newTab === 'retained') {
    loadRetainedMessages()
  } else if (newTab === 'will') {
    loadWillMessages()
  }
})

// 监听系统设置标签切换
watch(activeSettingsTab, (newTab) => {
  if (newTab === 'certificates' && isLoggedIn.value) {
    // 切换到RSA证书管理标签页时自动加载证书信息
    loadRSACertificateInfo()
  }
})

// 检查调试模式并设置缓存策略
async function checkDebugMode() {
  try {
    const response = await axios.get('/api/config/system')
    const configs = response.data.configs || []

    const debugModeConfig = configs.find(config => config.key_name === 'debug_mode')
    const isDebugMode = debugModeConfig?.value === 'true'

    if (isDebugMode) {
      // 禁用缓存
      const meta = document.createElement('meta')
      meta.httpEquiv = 'Cache-Control'
      meta.content = 'no-cache, no-store, must-revalidate'
      document.head.appendChild(meta)

      const meta2 = document.createElement('meta')
      meta2.httpEquiv = 'Pragma'
      meta2.content = 'no-cache'
      document.head.appendChild(meta2)

      const meta3 = document.createElement('meta')
      meta3.httpEquiv = 'Expires'
      meta3.content = '0'
      document.head.appendChild(meta3)

      console.log('🐛 调试模式已启用，缓存已禁用')
    }

    return isDebugMode
  } catch (error) {
    console.warn('检查调试模式失败:', error)
    return false
  }
}

onMounted(async () => {
  await loadDebugSettings()
  await checkAuthStatus()

  // 如果已登录且在仪表盘页面，加载仪表盘数据
  if (isLoggedIn.value && activeMenu.value === 'dashboard') {
    loadDashboardData()
  }

  // 初始化系统设置配置（串行加载避免频率限制）
  if (isLoggedIn.value) {
    try {
      // 首先检查调试模式（需要在登录后）
      await checkDebugMode()
      await new Promise(resolve => setTimeout(resolve, 300))

      // 串行加载安全配置，避免并发请求触发频率限制
      await loadSecurityConfig('security')
      await new Promise(resolve => setTimeout(resolve, 300)) // 300ms延迟

      await loadSecurityConfig('rate_limit')
      await new Promise(resolve => setTimeout(resolve, 300))

      await loadSecurityConfig('database')
      await new Promise(resolve => setTimeout(resolve, 300))

      await loadSecurityConfig('validation')
      await new Promise(resolve => setTimeout(resolve, 300))

      await loadSecurityConfig('captcha')
      await new Promise(resolve => setTimeout(resolve, 300))

      // 加载调试配置
      await loadDebugConfig()
      await new Promise(resolve => setTimeout(resolve, 300))

      // 备份功能已移除

      // 加载RSA证书信息
      await loadRSACertificateInfo()
      await new Promise(resolve => setTimeout(resolve, 300))

      // 加载设备类型
      await loadActiveDeviceTypes()
      await loadDeviceTypes()
      await new Promise(resolve => setTimeout(resolve, 300))
    } catch (error) {
      console.warn('Failed to load system settings data:', error)
    }
  }

  // 初始化主题处理
  updateProcessedTopics()

  // 移动端初始化
  checkMobile()
  window.addEventListener('resize', handleResize)

  // 添加页面可见性变化监听
  document.addEventListener('visibilitychange', () => {
    if (!document.hidden && isLoggedIn.value && !sseConnected.value) {
      // 页面变为可见且用户已登录但SSE未连接时，尝试重连
      console.log('📱 页面变为可见，检查SSE连接状态...')
      connectToSSE()
    }
  })

  // 移除滚动监听，新布局不需要滚动条
})

// 页面卸载时清理资源
onUnmounted(() => {
  disconnectFromSSE()
  window.removeEventListener('resize', handleResize)
  document.removeEventListener('visibilitychange', () => {})

  // 移除滚动监听清理，新布局不需要滚动条
})

// SSE连接状态管理
const sseReconnectAttempts = ref(0)
const maxReconnectAttempts = 10
const sseReconnectDelay = ref(3000) // 初始重连延迟3秒
const sseConnected = ref(false)
let reconnectTimer = null

// SSE连接管理
function connectToSSE() {
  // 如果已有连接，先关闭
  disconnectFromSSE()

  try {
    console.log('🔌 建立SSE连接...')

    // 检查是否已登录
    if (!isLoggedIn.value) {
      console.log('⚠️ 用户未登录，跳过SSE连接')
      return
    }

    // 检查token
    const token = getToken()
    if (!token || token === 'null' || token === 'undefined') {
      console.error('❌ 没有有效的认证token，无法建立SSE连接')
      return
    }

    // 构建带token的SSE URL
    const sseUrl = `http://localhost:8080/api/events?token=${encodeURIComponent(token)}`
    console.log('🔗 SSE URL:', sseUrl.replace(/token=[^&]+/, 'token=***'))

    eventSource = new EventSource(sseUrl)

    eventSource.onopen = function(event) {
      console.log('✅ SSE连接已建立')
      sseConnected.value = true
      sseReconnectAttempts.value = 0
      sseReconnectDelay.value = 3000 // 重置重连延迟

      // 清除重连定时器
      if (reconnectTimer) {
        clearTimeout(reconnectTimer)
        reconnectTimer = null
      }
    }

    // 监听心跳事件
    eventSource.addEventListener('client_heartbeat', function(event) {
      try {
        const data = JSON.parse(event.data)
        debugLog('💓 收到心跳事件:', data)
        handleHeartbeatEvent(data)
      } catch (error) {
        debugError('解析心跳事件失败:', error, 'Raw event:', event)
      }
    })

    // 监听客户端连接事件
    eventSource.addEventListener('client_connected', function(event) {
      try {
        const data = JSON.parse(event.data)
        debugLog('🔗 客户端连接:', data)
        handleClientConnected(data)
      } catch (error) {
        debugError('解析客户端连接事件失败:', error)
      }
    })

    // 监听客户端断开事件
    eventSource.addEventListener('client_disconnected', function(event) {
      try {
        const data = JSON.parse(event.data)
        debugLog('🔌 客户端断开:', data)
        handleClientDisconnected(data)
      } catch (error) {
        debugError('解析客户端断开事件失败:', error)
      }
    })

    // 监听客户端更新事件
    eventSource.addEventListener('client_updated', function(event) {
      try {
        const data = JSON.parse(event.data)
        debugLog('🔄 客户端更新:', data)
        handleClientUpdated(data)
      } catch (error) {
        debugError('解析客户端更新事件失败:', error)
      }
    })

    // 监听主题订阅事件
    eventSource.addEventListener('topic_subscribed', function(event) {
      try {
        const data = JSON.parse(event.data)
        debugLog('📋 主题订阅:', data)
        handleTopicSubscribed(data)
      } catch (error) {
        debugError('解析主题订阅事件失败:', error)
      }
    })

    // 监听主题取消订阅事件
    eventSource.addEventListener('topic_unsubscribed', function(event) {
      try {
        const data = JSON.parse(event.data)
        debugLog('📋 主题取消订阅:', data)
        handleTopicUnsubscribed(data)
      } catch (error) {
        debugError('解析主题取消订阅事件失败:', error)
      }
    })

    // 监听MQTT统计更新事件
    eventSource.addEventListener('mqtt_stats_updated', function(event) {
      try {
        const data = JSON.parse(event.data)
        debugLog('📊 MQTT统计更新:', data)
        handleMqttStatsUpdated(data)
      } catch (error) {
        debugError('解析MQTT统计更新事件失败:', error)
      }
    })

    // 通用事件处理器（用于调试）
    eventSource.onmessage = function(event) {
      try {
        const data = JSON.parse(event.data)
        debugLog('📡 收到SSE事件 (通用):', data)
      } catch (error) {
        debugError('解析SSE事件失败:', error, 'Raw event:', event)
      }
    }

    eventSource.onerror = function(event) {
      console.error('❌ SSE连接错误:', event)
      sseConnected.value = false

      // 检查是否是认证错误
      if (event.target.readyState === EventSource.CLOSED) {
        console.error('❌ SSE连接被服务器关闭，可能是认证失败')

        // 如果是认证错误，不进行重连
        if (sseReconnectAttempts.value === 0) {
          ElMessage.error('SSE连接认证失败，请重新登录')
        }
        return
      }

      // 自动重连逻辑（仅在用户已登录且页面可见时）
      if (isLoggedIn.value && sseReconnectAttempts.value < maxReconnectAttempts) {
        sseReconnectAttempts.value++

        console.log(`🔄 SSE连接断开，${sseReconnectDelay.value/1000}秒后进行第${sseReconnectAttempts.value}次重连...`)

        reconnectTimer = setTimeout(() => {
          if (isLoggedIn.value && !document.hidden) {
            connectToSSE()
          }
        }, sseReconnectDelay.value)

        // 指数退避：每次重连延迟翻倍，最大30秒
        sseReconnectDelay.value = Math.min(sseReconnectDelay.value * 2, 30000)
      } else if (sseReconnectAttempts.value >= maxReconnectAttempts) {
        console.error('❌ SSE重连次数已达上限，停止重连')
        ElMessage.error('网络连接不稳定，请检查网络后刷新页面')
      }
    }

  } catch (error) {
    console.error('❌ 创建SSE连接失败:', error)
  }
}

function disconnectFromSSE() {
  if (eventSource) {
    debugLog('🔌 关闭SSE连接')
    eventSource.close()
    eventSource = null
  }

  sseConnected.value = false
  sseReconnectAttempts.value = 0
  sseReconnectDelay.value = 3000

  // 清除重连定时器
  if (reconnectTimer) {
    clearTimeout(reconnectTimer)
    reconnectTimer = null
  }
}

// SSE事件处理函数
function handleClientConnected(eventData) {
  // 添加新客户端到列表
  if (eventData.client && !mqttStats.value.client_list.find(c => c.id === eventData.client.id)) {
    mqttStats.value.client_list.push(eventData.client)
    mqttStats.value.clients = mqttStats.value.client_list.length
    // 立即更新主题统计
    updateTopicsCount()
    debugLog('✅ 新客户端已添加:', eventData.client.id)
  }
}

function handleClientDisconnected(eventData) {
  // 从客户端列表中移除
  const index = mqttStats.value.client_list.findIndex(c => c.id === eventData.client_id)
  if (index !== -1) {
    mqttStats.value.client_list.splice(index, 1)
    mqttStats.value.clients = mqttStats.value.client_list.length
    // 立即更新主题统计
    updateTopicsCount()
    debugLog('✅ 客户端已移除:', eventData.client_id)
  }
}

function handleClientUpdated(eventData) {
  // 更新现有客户端信息
  if (eventData.client) {
    const index = mqttStats.value.client_list.findIndex(c => c.id === eventData.client.id)
    if (index !== -1) {
      // 更新现有客户端
      mqttStats.value.client_list[index] = eventData.client
      // 立即更新主题统计
      updateTopicsCount()
      debugLog('🔄 客户端信息已更新:', eventData.client.id)
    } else {
      // 如果客户端不存在，添加到列表
      mqttStats.value.client_list.push(eventData.client)
      mqttStats.value.clients = mqttStats.value.client_list.length
      // 立即更新主题统计
      updateTopicsCount()
      debugLog('✅ 新客户端已添加（通过更新事件）:', eventData.client.id)
    }
  }
}

// 实时更新主题统计
function updateTopicsCount() {
  // 基于当前客户端列表重新计算主题统计
  const topicSet = new Set()
  const topicList = []

  if (mqttStats.value.client_list) {
    mqttStats.value.client_list.forEach(client => {
      if (client.subscribed_topics && Array.isArray(client.subscribed_topics)) {
        client.subscribed_topics.forEach(topic => {
          topicSet.add(topic)
        })
      }
    })
  }

  // 更新主题列表和数量
  mqttStats.value.topics = topicSet.size
  mqttStats.value.topic_list = Array.from(topicSet)

  debugLog('📊 主题统计已实时更新:', {
    topics: mqttStats.value.topics,
    topic_list: mqttStats.value.topic_list
  })
}

function handleTopicSubscribed(eventData) {
  // 更新主题列表
  if (eventData.topic && !mqttStats.value.topic_list.find(t => t.topic === eventData.topic)) {
    mqttStats.value.topic_list.push({
      topic: eventData.topic,
      subscribers: 1,
      last_message: null
    })
    mqttStats.value.topics = mqttStats.value.topic_list.length
    debugLog('✅ 新主题已添加:', eventData.topic)
  } else {
    // 更新订阅者数量
    const topicIndex = mqttStats.value.topic_list.findIndex(t => t.topic === eventData.topic)
    if (topicIndex !== -1) {
      mqttStats.value.topic_list[topicIndex].subscribers++
      debugLog('✅ 主题订阅者数量已更新:', eventData.topic)
    }
  }

  // 更新处理后的主题列表
  updateProcessedTopics()
}

function handleTopicUnsubscribed(eventData) {
  // 更新主题订阅者数量
  const topicIndex = mqttStats.value.topic_list.findIndex(t => t.topic === eventData.topic)
  if (topicIndex !== -1) {
    mqttStats.value.topic_list[topicIndex].subscribers--

    // 如果没有订阅者了，移除主题
    if (mqttStats.value.topic_list[topicIndex].subscribers <= 0) {
      mqttStats.value.topic_list.splice(topicIndex, 1)
      mqttStats.value.topics = mqttStats.value.topic_list.length
      debugLog('✅ 主题已移除:', eventData.topic)
    } else {
      debugLog('✅ 主题订阅者数量已更新:', eventData.topic)
    }
  }

  // 更新处理后的主题列表
  updateProcessedTopics()
}

function handleMqttStatsUpdated(eventData) {
  // 处理MQTT统计更新事件
  if (eventData.stats) {
    debugLog('📊 更新MQTT统计数据:', eventData.stats)

    // 只更新非实时计算的统计数据
    mqttStats.value.blacklist_count = eventData.stats.blacklist_count || 0
    mqttStats.value.failed_auth_count = eventData.stats.failed_auth_count || 0

    // 客户端和主题数据由实时事件处理，这里不覆盖
    // 只在初始加载或客户端列表为空时才更新
    if (!mqttStats.value.client_list || mqttStats.value.client_list.length === 0) {
      if (eventData.stats.client_list) {
        mqttStats.value.client_list = eventData.stats.client_list
        mqttStats.value.clients = eventData.stats.clients || 0
        debugLog('👥 客户端列表已通过SSE初始化，当前数量:', eventData.stats.client_list.length)
      }

      if (eventData.stats.topic_list) {
        mqttStats.value.topic_list = eventData.stats.topic_list
        mqttStats.value.topics = eventData.stats.topics || 0
        updateProcessedTopics()
        debugLog('📋 主题列表已通过SSE初始化，当前数量:', eventData.stats.topic_list.length)
      }
    } else {
      debugLog('⏭️ 跳过客户端和主题数据更新（使用实时数据）')
    }

    // 更新黑名单和失败认证列表
    if (eventData.stats.blacklist) {
      mqttStats.value.blacklist = eventData.stats.blacklist
    }
    if (eventData.stats.failed_auth) {
      mqttStats.value.failed_auth = eventData.stats.failed_auth
    }
  }
}

// 设备管理相关函数
async function loadDevices() {
  devicesLoading.value = true
  try {
    const response = await axios.get('/api/devices')
    // 处理后端返回的分页数据结构
    if (response.data && response.data.devices) {
      devices.value = Array.isArray(response.data.devices) ? response.data.devices : []
      debugLog('设备列表加载成功:', devices.value.length)
    } else {
      // 确保 devices.value 始终是数组
      devices.value = Array.isArray(response.data) ? response.data : []
      debugLog('设备列表加载成功:', devices.value.length)
    }
  } catch (e) {
    debugError('加载设备列表失败:', e)
    ElMessage.error(e.response?.data?.error || '加载设备列表失败')
    // 出错时确保 devices 是空数组
    devices.value = []
  }
  devicesLoading.value = false
}

// 设备类型管理函数

// 加载所有设备类型
async function loadDeviceTypes() {
  deviceTypesLoading.value = true
  try {
    const response = await axios.get('/api/device-types')
    deviceTypes.value = Array.isArray(response.data.device_types) ? response.data.device_types : []
    debugLog('设备类型列表加载成功:', deviceTypes.value.length)
  } catch (e) {
    debugError('加载设备类型列表失败:', e)
    ElMessage.error(e.response?.data?.error || '加载设备类型列表失败')
    deviceTypes.value = []
  }
  deviceTypesLoading.value = false
}

// 加载启用的设备类型
async function loadActiveDeviceTypes(forceReload = false) {
  // 如果不是强制重新加载，且距离上次加载不到30秒，且已有数据，则跳过
  const now = Date.now()
  if (!forceReload && activeDeviceTypes.value.length > 0 && (now - deviceTypesLastLoaded.value) < 30000) {
    debugLog('设备类型数据仍然有效，跳过重新加载')
    return
  }

  try {
    const response = await axios.get('/api/device-types/active')
    activeDeviceTypes.value = Array.isArray(response.data.device_types) ? response.data.device_types : []
    deviceTypesLastLoaded.value = now
    debugLog('启用设备类型列表加载成功:', activeDeviceTypes.value.length)

    // 如果没有启用的设备类型，尝试加载所有设备类型
    if (activeDeviceTypes.value.length === 0) {
      debugLog('没有启用的设备类型，尝试加载所有设备类型...')
      const allTypesResponse = await axios.get('/api/device-types')
      const allTypes = Array.isArray(allTypesResponse.data.device_types) ? allTypesResponse.data.device_types : []
      // 只使用启用的类型
      activeDeviceTypes.value = allTypes.filter(type => type.is_active)
      debugLog('从所有设备类型中筛选启用类型:', activeDeviceTypes.value.length)
    }
  } catch (e) {
    debugError('加载启用设备类型列表失败:', e)
    // 只有在没有缓存数据时才显示错误消息
    if (activeDeviceTypes.value.length === 0) {
      ElMessage.error(e.response?.data?.error || '加载启用设备类型列表失败')
    }
    // 保持现有数据不变，除非是强制重新加载
    if (forceReload) {
      activeDeviceTypes.value = []
    }
  }
}

// 设备类型辅助函数

// 获取设备类型名称
function getDeviceTypeName(typeCode) {
  if (!typeCode) return '未分类'
  const deviceType = activeDeviceTypes.value.find(dt => dt.type_code === typeCode)
  return deviceType ? deviceType.type_name : typeCode
}

// 获取设备类型图标
function getDeviceTypeIcon(typeCode) {
  if (!typeCode) return 'fas fa-question-circle'
  const deviceType = activeDeviceTypes.value.find(dt => dt.type_code === typeCode)
  return deviceType ? deviceType.icon : 'fas fa-microchip'
}

// 获取设备类型颜色
function getDeviceTypeColor(typeCode) {
  if (!typeCode) return '#9ca3af'
  const deviceType = activeDeviceTypes.value.find(dt => dt.type_code === typeCode)
  return deviceType ? deviceType.color : '#6b7280'
}



// 设备类型管理函数

// 清空所有设备类型
async function clearAllDeviceTypes() {
  try {
    await ElMessageBox.confirm(
      '确定要清空所有用户自定义的设备类型吗？此操作不可恢复！\n\n注意：\n• 系统默认类型不会被删除\n• 如果系统中有设备，将无法清空设备类型',
      '确认清空',
      {
        confirmButtonText: '确定清空',
        cancelButtonText: '取消',
        type: 'error',
        dangerouslyUseHTMLString: true
      }
    )

    await axios.delete('/api/device-types/clear-all')
    ElMessage.success('所有设备类型已清空')
    loadDeviceTypes()
    loadActiveDeviceTypes(true) // 强制重新加载
  } catch (e) {
    if (e !== 'cancel') {
      ElMessage.error(e.response?.data?.error || '清空设备类型失败')
    }
  }
}

// 显示新增设备类型对话框
function showAddDeviceTypeDialog() {
  Object.assign(deviceTypeForm, {
    id: null,
    type_code: '',
    type_name: '',
    description: '',
    icon: 'fas fa-microchip',
    color: '#6b7280',
    is_active: true,
    sort_order: 1
  })
  isDeviceTypeEditMode.value = false
  showIconSelector.value = false
  iconSearchKeyword.value = ''
  deviceTypeDialogVisible.value = true
}

// 显示编辑设备类型对话框
function showEditDeviceTypeDialog(deviceType) {
  Object.assign(deviceTypeForm, deviceType)
  // 确保用户编辑的设备类型排序值至少为1（系统类型使用0）
  if (deviceTypeForm.sort_order < 1) {
    deviceTypeForm.sort_order = 1
  }
  isDeviceTypeEditMode.value = true
  showIconSelector.value = false
  iconSearchKeyword.value = ''
  deviceTypeDialogVisible.value = true
}

// 图标选择器相关函数

// 筛选图标
const filteredIcons = computed(() => {
  if (!iconSearchKeyword.value) {
    return availableIcons
  }
  return availableIcons.filter(icon =>
    icon.name.toLowerCase().includes(iconSearchKeyword.value.toLowerCase()) ||
    icon.class.toLowerCase().includes(iconSearchKeyword.value.toLowerCase())
  )
})

// 检查是否有用户自定义的设备类型
const hasUserDefinedDeviceTypes = computed(() => {
  return deviceTypes.value.some(deviceType => !deviceType.is_system)
})

// 监听设备类型列表变化，自动设置默认值
watch(activeDeviceTypes, async (newTypes) => {
  // 只在创建设备对话框打开且没有选中设备类型时设置默认值
  if (deviceDialogVisible.value && !isDeviceEditMode.value && !deviceForm.device_type && newTypes.length > 0) {
    await nextTick()
    deviceForm.device_type = newTypes[0].type_code
  }
}, { immediate: false })

// 选择图标
function selectIcon(iconClass) {
  deviceTypeForm.icon = iconClass
  showIconSelector.value = false
  iconSearchKeyword.value = ''
}

// 获取图标名称
function getIconName(iconClass) {
  const icon = availableIcons.find(icon => icon.class === iconClass)
  return icon ? icon.name : iconClass
}

// 关闭图标选择器
function closeIconSelector() {
  showIconSelector.value = false
  iconSearchKeyword.value = ''
}

// 切换到设备类型标签页
function switchToDeviceTypes() {
  deviceDialogVisible.value = false
  activeDeviceSubTab.value = 'device-types'
}

// 点击外部关闭指令
const vClickOutside = {
  mounted(el, binding) {
    el.clickOutsideEvent = function(event) {
      if (!(el === event.target || el.contains(event.target))) {
        binding.value()
      }
    }
    document.addEventListener('click', el.clickOutsideEvent)
  },
  unmounted(el) {
    document.removeEventListener('click', el.clickOutsideEvent)
  }
}

// 保存设备类型
async function saveDeviceType() {
  if (!deviceTypeForm.type_code || !deviceTypeForm.type_name) {
    ElMessage.error('请填写完整的设备类型信息')
    return
  }

  try {
    if (isDeviceTypeEditMode.value) {
      await axios.put(`/api/device-types/${deviceTypeForm.id}`, {
        type_name: deviceTypeForm.type_name,
        description: deviceTypeForm.description,
        icon: deviceTypeForm.icon,
        color: deviceTypeForm.color,
        is_active: deviceTypeForm.is_active,
        sort_order: deviceTypeForm.sort_order
      })
      ElMessage.success('设备类型更新成功')
    } else {
      await axios.post('/api/device-types', {
        type_code: deviceTypeForm.type_code,
        type_name: deviceTypeForm.type_name,
        description: deviceTypeForm.description,
        icon: deviceTypeForm.icon,
        color: deviceTypeForm.color,
        sort_order: deviceTypeForm.sort_order
      })
      ElMessage.success('设备类型创建成功')
    }

    deviceTypeDialogVisible.value = false
    loadDeviceTypes()
    loadActiveDeviceTypes(true) // 强制重新加载
  } catch (e) {
    ElMessage.error(e.response?.data?.error || '保存设备类型失败')
  }
}

// 删除设备类型
async function deleteDeviceType(deviceType) {
  try {
    await ElMessageBox.confirm(`确定要删除设备类型 "${deviceType.type_name}" 吗？`, '确认删除', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })

    await axios.delete(`/api/device-types/${deviceType.id}`)
    ElMessage.success('设备类型删除成功')
    loadDeviceTypes()
    loadActiveDeviceTypes(true) // 强制重新加载
  } catch (e) {
    if (e !== 'cancel') {
      ElMessage.error(e.response?.data?.error || '删除设备类型失败')
    }
  }
}

// 筛选设备
const filteredDevices = computed(() => {
  // 确保 devices.value 是数组
  if (!Array.isArray(devices.value)) {
    return []
  }

  if (!deviceSearchKeyword.value) {
    return devices.value
  }

  return devices.value.filter(device =>
    device.device_id?.toLowerCase().includes(deviceSearchKeyword.value.toLowerCase()) ||
    device.device_name?.toLowerCase().includes(deviceSearchKeyword.value.toLowerCase()) ||
    device.username?.toLowerCase().includes(deviceSearchKeyword.value.toLowerCase())
  )
})

// 批量选择设备
function handleDeviceSelectionChange(selection) {
  selectedDeviceIds.value = selection.map(device => device.device_id)
}

// 全选/取消全选设备
function handleSelectAllDevices(selection) {
  selectedDeviceIds.value = selection.length > 0 ? filteredDevices.value.map(device => device.device_id) : []
}

// 显示新建设备对话框
async function showCreateDeviceDialog() {
  isDeviceEditMode.value = false

  // 加载设备类型，确保数据可用，最多重试3次
  let retryCount = 0
  const maxRetries = 3

  while (retryCount < maxRetries) {
    await loadActiveDeviceTypes()

    if (activeDeviceTypes.value.length > 0) {
      break // 成功加载到设备类型，退出重试循环
    }

    retryCount++
    if (retryCount < maxRetries) {
      debugLog(`设备类型加载失败，正在重试 ${retryCount}/${maxRetries}...`)
      await new Promise(resolve => setTimeout(resolve, 1000)) // 等待1秒后重试
    }
  }

  // 重置表单（在加载设备类型之后）
  resetDeviceForm()

  // 如果有设备类型，设置为第一个可用的类型
  if (activeDeviceTypes.value.length > 0) {
    // 使用 nextTick 确保 DOM 更新后再设置值
    await nextTick()
    const firstType = activeDeviceTypes.value[0]

    // 强制触发响应式更新以确保选择器正确显示
    deviceForm.device_type = ''
    await nextTick()
    deviceForm.device_type = firstType.type_code
    await nextTick()
  } else {
    ElMessage.warning('未能加载设备类型列表，请稍后重试或联系管理员')
  }

  deviceDialogVisible.value = true
}

// 显示编辑设备对话框
async function showEditDeviceDialog(device) {
  // 先加载设备类型，最多重试3次
  let retryCount = 0
  const maxRetries = 3

  while (retryCount < maxRetries) {
    await loadActiveDeviceTypes()

    if (activeDeviceTypes.value.length > 0) {
      break // 成功加载到设备类型，退出重试循环
    }

    retryCount++
    if (retryCount < maxRetries) {
      debugLog(`设备类型加载失败，正在重试 ${retryCount}/${maxRetries}...`)
      await new Promise(resolve => setTimeout(resolve, 1000)) // 等待1秒后重试
    }
  }

  if (activeDeviceTypes.value.length === 0) {
    ElMessage.warning('未能加载设备类型列表，请稍后重试或联系管理员')
  }

  resetDeviceForm()
  isDeviceEditMode.value = true

  // 调试日志：查看设备数据
  console.log('编辑设备数据:', device)
  console.log('设备is_active状态:', device.is_active)

  Object.assign(deviceForm, device)

  // 调试日志：查看表单数据
  console.log('表单赋值后is_active:', deviceForm.is_active)

  if (device.authorization) {
    // 保存设备的 is_active 状态，避免被授权信息覆盖
    const deviceIsActive = deviceForm.is_active
    Object.assign(deviceForm, device.authorization)
    // 恢复设备的 is_active 状态
    deviceForm.is_active = deviceIsActive


  }

  deviceDialogVisible.value = true
}

// 重置设备表单
function resetDeviceForm() {
  Object.assign(deviceForm, {
    id: null,
    device_id: '',
    device_name: '',
    user_id: null,
    device_type: '', // 不设置默认值，等待加载设备类型后设置
    is_active: true,
    max_usage: 100,
    expire_time: null,
    account_info_encryption: true
  })
}

// 保存设备
async function saveDevice() {
  if (!deviceForm.device_id || !deviceForm.device_name || !deviceForm.user_id) {
    ElMessage.error('请填写完整的设备信息')
    return
  }

  // 检查是否有可用的设备类型
  if (!isDeviceEditMode.value && activeDeviceTypes.value.length === 0) {
    ElMessage.error('请先创建设备类型')
    return
  }

  // 如果没有选择设备类型且有可用类型，使用第一个
  if (!deviceForm.device_type && activeDeviceTypes.value.length > 0) {
    deviceForm.device_type = activeDeviceTypes.value[0].type_code
  }

  deviceSaveLoading.value = true
  try {
    const deviceData = {
      device_id: deviceForm.device_id,
      device_name: deviceForm.device_name,
      user_id: deviceForm.user_id,
      device_type: deviceForm.device_type,
      is_active: deviceForm.is_active
    }

    const authData = {
      max_usage: deviceForm.max_usage,
      expire_time: deviceForm.expire_time,
      usage_control_enabled: true, // 始终启用使用控制
      account_info_encryption: deviceForm.account_info_encryption
    }

    if (isDeviceEditMode.value) {
      // 使用device_id而不是数据库主键id
      await axios.put(`/api/devices/${deviceForm.device_id}`, { ...deviceData, authorization: authData })
      ElMessage.success('设备更新成功')
    } else {
      await axios.post('/api/devices', { ...deviceData, authorization: authData })
      ElMessage.success('设备创建成功')
    }

    deviceDialogVisible.value = false
    loadDevices()
  } catch (e) {
    ElMessage.error(e.response?.data?.error || '保存设备失败')
  }
  deviceSaveLoading.value = false
}

// 删除设备
async function deleteDevice(device) {
  try {
    await ElMessageBox.confirm(`确定要删除设备 "${device.device_name}" 吗？`, '确认删除', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })

    // 使用device_id而不是数据库主键id
    await axios.delete(`/api/devices/${device.device_id}`)
    ElMessage.success('设备删除成功')
    loadDevices()
  } catch (e) {
    if (e !== 'cancel') {
      ElMessage.error(e.response?.data?.error || '删除设备失败')
    }
  }
}

// 批量操作设备
async function batchToggleDevices(enable) {
  if (selectedDeviceIds.value.length === 0) {
    ElMessage.warning('请先选择要操作的设备')
    return
  }

  const action = enable ? 'enable' : 'disable'
  const actionText = enable ? '启用' : '禁用'

  try {
    await ElMessageBox.confirm(
      `确定要${actionText} ${selectedDeviceIds.value.length} 个设备吗？`,
      `确认批量${actionText}`,
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    await axios.post('/api/devices/batch-toggle', {
      device_ids: selectedDeviceIds.value,
      action: action
    })

    ElMessage.success(`已${actionText} ${selectedDeviceIds.value.length} 个设备`)
    selectedDeviceIds.value = []
    loadDevices()
  } catch (e) {
    if (e !== 'cancel') {
      ElMessage.error(e.response?.data?.error || `批量${actionText}设备失败`)
    }
  }
}

// 批量启用设备（保持向后兼容）
async function batchEnableDevices() {
  await batchToggleDevices(true)
}

// 批量禁用设备（保持向后兼容）
async function batchDisableDevices() {
  await batchToggleDevices(false)
}

// 检查设备是否在线（通过MQTT客户端列表）
function isDeviceOnline(deviceId) {
  if (!mqttStats.value.client_list || !Array.isArray(mqttStats.value.client_list)) {
    return false
  }
  return mqttStats.value.client_list.some(client => client.id === deviceId)
}



// 重置设备使用次数
async function resetDeviceUsage(device) {
  try {
    await ElMessageBox.confirm(`确定要重置设备 "${device.device_name}" 的使用次数吗？`, '确认重置', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })

    await axios.put(`/api/devices/${device.device_id}/reset-usage`)
    ElMessage.success('设备使用次数重置成功')
    loadDevices()
  } catch (e) {
    if (e !== 'cancel') {
      ElMessage.error(e.response?.data?.error || '重置设备使用次数失败')
    }
  }
}

// 获取授权状态标签类型
function getAuthStatusType(device) {
  if (!device.authorization) return 'info'
  if (!device.authorization.is_active) return 'danger'
  if (device.authorization.expire_time && new Date(device.authorization.expire_time) < new Date()) return 'warning'
  if (device.authorization.current_usage >= device.authorization.max_usage) return 'warning'
  return 'success'
}

// 获取授权状态文本
function getAuthStatusText(device) {
  if (!device.authorization) return '未配置'
  if (!device.authorization.is_active) return '已禁用'
  if (device.authorization.expire_time && new Date(device.authorization.expire_time) < new Date()) return '已过期'
  if (device.authorization.current_usage >= device.authorization.max_usage) return '已用完'
  return '正常'
}

// 获取使用百分比
function getUsagePercentage(authorization) {
  if (!authorization) return 0
  if (authorization.max_usage <= 0) return 0
  return Math.min(100, (authorization.current_usage / authorization.max_usage) * 100)
}

// 格式化过期时间显示
function formatExpireTime(expireTime) {
  if (!expireTime) {
    return '无限制'
  }

  // 检查是否是无效时间（如 0001-01-01）
  const date = new Date(expireTime)
  if (date.getFullYear() < 1900) {
    return '无限制'
  }

  return formatDateTime(expireTime)
}

// 检查是否所有设备都被选中
const isAllDevicesSelected = computed(() => {
  return filteredDevices.value.length > 0 && selectedDeviceIds.value.length === filteredDevices.value.length
})

// 切换全选状态
function toggleAllDevicesSelection() {
  if (isAllDevicesSelected.value) {
    selectedDeviceIds.value = []
  } else {
    selectedDeviceIds.value = filteredDevices.value.map(device => device.device_id)
  }
}
</script>

<style>
.app-container {
  min-height: 100vh;
  height: 100%;
  background-color: var(--color-bg-secondary);
  padding: 0;
}

/* 所有分辨率都移除padding，使用内部组件控制间距 */

.page-header {
  text-align: center;
  margin-bottom: 50px;
  color: white;
  padding: 60px 20px 0;
}

.page-title {
  font-size: 2.8em;
  margin-bottom: 15px;
  font-weight: 600;
  text-shadow: 0 3px 6px rgba(0,0,0,0.3);
  color: white;
  letter-spacing: 0.5px;
}

.page-title i {
  margin-right: 15px;
  font-size: 1em;
  color: white;
  opacity: 0.9;
}

.page-subtitle {
  font-size: 1.2em;
  opacity: 1;
  margin: 0;
  font-weight: 400;
  color: #ffffff;
  letter-spacing: 2px;
  text-shadow: 0 2px 6px rgba(0,0,0,0.5);
  background: rgba(255, 255, 255, 0.1);
  padding: 8px 16px;
  border-radius: 20px;
  border: 1px solid rgba(255, 255, 255, 0.2);
  backdrop-filter: blur(5px);
  display: inline-block;
  margin-top: 10px;
}

/* 高分辨率标题适配 */
@media (min-width: 1920px) {
  .page-title {
    font-size: 3.5em;
  }
  
  .page-subtitle {
    font-size: 1.5em;
  }
}

/* 中等分辨率标题适配 */
@media (min-width: 1200px) and (max-width: 1919px) {
  .page-title {
    font-size: 3em;
  }
  
  .page-subtitle {
    font-size: 1.3em;
  }
}

.login-section {
  display: flex;
  justify-content: center;
  align-items: flex-start;
  margin-bottom: 60px;
  padding: 0 20px;
}

.login-card {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 16px;
  padding: 45px;
  box-shadow: 0 20px 40px rgba(0,0,0,0.12);
  max-width: 420px;
  width: 100%;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.3);
  position: relative;
  overflow: hidden;
}

.login-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, #667eea, #764ba2, #667eea);
  background-size: 200% 100%;
  animation: shimmer 3s ease-in-out infinite;
}

@keyframes shimmer {
  0%, 100% { background-position: 200% 0; }
  50% { background-position: -200% 0; }
}

/* 高分辨率登录卡片适配 */
@media (min-width: 1920px) {
  .login-card {
    max-width: 500px;
    padding: 50px;
  }
}

/* 中等分辨率登录卡片适配 */
@media (min-width: 1200px) and (max-width: 1919px) {
  .login-card {
    max-width: 450px;
    padding: 45px;
  }
}

/* 移动端登录卡片适配 */
@media (max-width: 768px) {
  .login-card {
    padding: 30px 20px;
    max-width: 100%;
    margin: 0 10px;
  }
}

.login-header {
  text-align: center;
  margin-bottom: 40px;
}

.login-icon {
  width: 60px;
  height: 60px;
  margin: 0 auto 20px;
  background: linear-gradient(135deg, #667eea, #764ba2);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 8px 20px rgba(102, 126, 234, 0.3);
}

.login-icon i {
  font-size: 24px;
  color: white;
}

.login-header h2 {
  color: #333;
  font-size: 1.8em;
  margin-bottom: 8px;
  font-weight: 600;
}

.login-header p {
  color: #666;
  margin: 0;
  font-size: 14px;
}

.login-form {
  margin-bottom: 20px;
}

.login-form .el-form-item {
  margin-bottom: 24px;
}

.login-form .el-input {
  border-radius: 12px;
}

.login-form .el-input__wrapper {
  border-radius: 12px;
  padding: 12px 16px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
  border: 1px solid rgba(102, 126, 234, 0.2);
  transition: all 0.3s ease;
}

.login-form .el-input__wrapper:hover {
  border-color: rgba(102, 126, 234, 0.4);
  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.15);
}

.login-form .el-input__wrapper.is-focus {
  border-color: #667eea;
  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.25);
}

.login-form .el-input__prefix {
  color: #667eea;
}

.login-button {
  width: 100%;
  height: 52px;
  font-size: 16px;
  font-weight: 600;
  border-radius: 12px;
  background: linear-gradient(135deg, #667eea, #764ba2);
  border: none;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.login-button::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
  transition: left 0.5s;
}

.login-button:hover::before {
  left: 100%;
}

.login-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 12px 30px rgba(102, 126, 234, 0.4);
}


.user-info-card {
  background: white;
  border-radius: 12px;
  padding: 30px;
  box-shadow: 0 20px 40px rgba(0,0,0,0.1);
  max-width: 400px;
  width: 100%;
  display: flex;
  align-items: center;
  gap: 20px;
}

.user-avatar {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  background-color: var(--color-primary);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 24px;
}

.user-details {
  flex: 1;
}

.user-details h3 {
  margin: 0 0 5px 0;
  color: #333;
  font-size: 1.2em;
}

.user-details p {
  margin: 0;
  color: #666;
}

.remaining-count {
  color: #667eea;
  font-weight: 600;
}

.logout-btn {
  border-radius: 6px;
}

/* 后台管理页面样式 */
.admin-layout {
  display: flex;
  height: 100vh;
  background-color: var(--color-bg-secondary);
}

/* 移动端后台管理适配 */
@media (max-width: 768px) {
  .admin-layout {
    flex-direction: column;
    height: auto;
    min-height: 100vh;
  }
}

.sidebar {
  width: 250px;
  background-color: var(--color-sidebar-bg);
  color: var(--color-sidebar-text);
  flex-shrink: 0;
}

/* 高分辨率侧边栏适配 */
@media (min-width: 1920px) {
  .sidebar {
    width: 300px;
  }
}

/* 中等分辨率侧边栏适配 */
@media (min-width: 1200px) and (max-width: 1919px) {
  .sidebar {
    width: 280px;
  }
}

/* 小屏幕PC侧边栏适配 */
@media (min-width: 768px) and (max-width: 1199px) {
  .sidebar {
    width: 220px;
  }
}

/* 移动端侧边栏适配 */
@media (max-width: 768px) {
  .sidebar {
    width: 100%;
    height: auto;
  }
}

.sidebar-header {
  padding: 20px;
  border-bottom: 1px solid #34495e;
  text-align: center;
}

.sidebar-header h3 {
  margin: 0 0 5px 0;
  font-size: 1.2em;
}

.sidebar-header .version {
  font-size: 0.8em;
  color: #95a5a6;
}

.sidebar-menu {
  border: none;
}

.sidebar-menu .el-menu-item {
  height: 50px;
  line-height: 50px;
  border-bottom: 1px solid #34495e;
}

/* 移动端菜单适配 */
@media (max-width: 768px) {
  .sidebar-menu {
    display: flex;
    overflow-x: auto;
    white-space: nowrap;
  }
  
  .sidebar-menu .el-menu-item {
    flex: 0 0 auto;
    min-width: 120px;
    text-align: center;
    border-bottom: none;
    border-right: 1px solid #34495e;
  }
  
  .sidebar-menu .el-menu-item:last-child {
    border-right: none;
  }
}

.sidebar-menu .el-menu-item:hover {
  background-color: var(--color-sidebar-hover);
}

.sidebar-menu .el-menu-item.is-active {
  background-color: var(--color-primary);
  color: #ffffff !important;
  font-weight: 600;
}

.sidebar-menu .el-menu-item.is-active i {
  color: #ffffff !important;
}

.sidebar-menu .el-menu-item.is-active span {
  color: #ffffff !important;
}

.sidebar-menu .el-menu-item {
  display: flex;
  align-items: center;
  justify-content: flex-start;
}

.sidebar-menu .el-menu-item i {
  margin-right: 10px;
  font-size: 16px;
  flex-shrink: 0;
}

.sidebar-menu .el-menu-item span {
  flex: 1;
  text-align: left;
}

.main-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  background-color: var(--color-bg-secondary);
}

.header {
  height: 60px;
  background-color: var(--color-bg-primary);
  border-bottom: 1px solid var(--color-border-base);
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 var(--spacing-xl);
  box-shadow: var(--shadow-sm);
}

.header-left h2 {
  margin: 0;
  color: #333;
  font-size: 1.4em;
}

.header-right .user-info {
  display: flex;
  align-items: center;
  gap: 15px;
}

/* 移动端顶部导航适配 */
@media (max-width: 768px) {
  .header {
    padding: 0 10px;
    flex-direction: column;
    height: auto;
    padding: 15px 10px;
  }
  
  .header-left {
    margin-bottom: 10px;
  }
  
  .header-right .user-info {
    gap: 10px;
    font-size: 14px;
  }
  
  .header-right .user-info .welcome-text,
  .header-right .user-info .remaining-uses {
    display: none;
  }
}

.welcome-text {
  color: #333;
  font-weight: 500;
}

.remaining-uses {
  color: #666;
  font-size: 0.9em;
}

.content-area {
  flex: 1;
  padding: var(--spacing-xl);
  overflow-y: auto;
  background-color: var(--color-bg-secondary);
}

/* 高分辨率内容区域适配 */
@media (min-width: 1920px) {
  .content-area {
    padding: 40px;
  }
}

/* 中等分辨率内容区域适配 */
@media (min-width: 1200px) and (max-width: 1919px) {
  .content-area {
    padding: 30px;
  }
}

/* 小屏幕PC内容区域适配 */
@media (min-width: 768px) and (max-width: 1199px) {
  .content-area {
    padding: 20px;
  }
}

/* 移动端内容区域适配 */
@media (max-width: 768px) {
  .content-area {
    padding: 10px;
  }
}

/* 批量操作工具栏样式 */
.batch-actions-card {
  background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%);
  border: 1px solid #0ea5e9;
  box-shadow: 0 4px 15px rgba(14, 165, 233, 0.15);
}

.batch-actions-card .card-content {
  padding: 1rem;
}

.batch-info {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-weight: 600;
  color: #0c4a6e;
  font-size: 1rem;
}

.batch-info i {
  color: #059669;
  font-size: 1.2rem;
}

.batch-actions {
  display: flex;
  gap: 0.5rem;
}



/* 仪表盘管理样式 */
.dashboard-management {
  width: 100%;
  padding: 20px;
}

.dashboard-content {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20px;
  margin-top: 20px;
}

.dashboard-section {
  background: white;
  border-radius: 6px;
  padding: 12px 15px; /* 缩小内边距 */
  border: 1px solid #e0e0e0;
}

.dashboard-section h3 {
  margin: 0 0 15px 0;
  color: #333;
  font-size: 1.1em;
  border-bottom: 2px solid #e0e0e0;
  padding-bottom: 8px;
}

.system-info {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.info-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 0;
  border-bottom: 1px solid #f0f0f0;
}

.info-row:last-child {
  border-bottom: none;
}

.info-label {
  font-size: 14px;
  color: #666;
  font-weight: 500;
}

.info-value {
  font-size: 14px;
  color: #333;
  font-weight: 600;
}

.quick-actions {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 12px;
}

.action-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  padding: 12px 16px;
  background: #f8f9fa;
  border: 1px solid #e0e0e0;
  border-radius: 6px;
  color: #333;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.action-btn:hover {
  background: #e9ecef;
  border-color: #409eff;
  color: #409eff;
  transform: translateY(-1px);
}

.action-btn i {
  font-size: 16px;
}

/* 移动端仪表盘适配 */
@media (max-width: 768px) {
  .dashboard-management {
    width: 100%;
    padding: 15px;
  }
  
  .dashboard-content {
    grid-template-columns: 1fr;
    gap: 15px;
  }
  
  .dashboard-section {
    padding: 10px 12px; /* 移动端进一步缩小 */
  }
  
  .quick-actions {
    grid-template-columns: 1fr;
    gap: 10px;
  }
  
  .action-btn {
    padding: 10px 14px;
    font-size: 13px;
  }
}


/* 占位符内容样式 */
.placeholder-content {
  text-align: center;
  padding: 80px 20px;
  color: #666;
}

.placeholder-icon {
  font-size: 48px;
  margin-bottom: 20px;
  color: #ccc;
}

.placeholder-content h3 {
  margin: 0 0 10px 0;
  color: #333;
}

.placeholder-content p {
  margin: 0;
  color: #666;
}

/* 用户管理样式 */
.user-management {
  width: 100%;
  min-height: calc(100vh - 140px); /* 改为min-height，允许内容超出时滚动 */
  display: flex;
  flex-direction: column;
  /* 移除 overflow: hidden，允许垂直滚动 */
}

.user-toolbar {
  display: flex;
  justify-content: flex-start;
  align-items: center;
  margin-bottom: 20px;
  padding: 0 0 20px 0;
  border-bottom: 1px solid #eee;
  gap: 20px;
}

.search-box {
  display: flex;
  align-items: center;
}

.user-table-container {
  flex: 1;
  overflow: hidden;
  padding: 0 15px 15px 15px; /* 缩小内边距 */
}


/* 移动端用户管理适配 */
@media (max-width: 768px) {
  .user-toolbar {
    flex-direction: column;
    gap: 15px;
    align-items: stretch;
  }
  
  .search-box {
    width: 100%;
  }
  
  .search-box .el-input {
    width: 100% !important;
  }
  
  .user-table-container {
    overflow-x: auto;
    padding: 0 10px 10px 10px;
  }
  
  .logs-header {
    padding: 10px 10px 10px 10px;
  }
}

/* 用户表格操作按钮样式 */
.user-table-container .el-button {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  text-align: center;
  margin-right: 8px;
}

.user-table-container .el-button i {
  margin-right: 4px;
}

/* 用户工具栏按钮样式 */
.user-toolbar .el-button {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  text-align: center;
}

.user-toolbar .el-button i {
  margin-right: 4px;
}

/* MQTT管理样式 - 无滚动条设计 */
.mqtt-management {
  width: 100%;
  height: calc(100vh - 80px); /* 固定高度，适应屏幕 */
  display: flex;
  flex-direction: column;
  overflow: hidden; /* 禁用滚动条 */
}

/* 内容卡片样式 - 合理紧凑 */
.content-card {
  background: white;
  border-radius: 8px;
  border: 1px solid #e0e0e0;
  overflow: hidden;
  margin-bottom: 10px; /* 合理间距 */
  display: flex;
  flex-direction: column;
  max-height: calc((100vh - 280px) / 2); /* 限制每个卡片最大高度为可用空间的一半 */
}

.card-header {
  background: #f8f9fa;
  padding: 8px 12px; /* 合理内边距 */
  border-bottom: 1px solid #e0e0e0;
  font-weight: 600;
  color: #333;
  font-size: 14px; /* 正常字体 */
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 8px;
  height: 36px; /* 合理高度 */
  box-sizing: border-box;
  flex-shrink: 0;
}

.card-header i {
  color: #666;
  margin-right: 8px;
}

.card-actions {
  display: flex;
  gap: 8px;
}

.card-content {
  padding: 12px 16px; /* 合理内边距 */
  flex: 1;
  overflow: hidden; /* 禁用滚动 */
  min-height: 0;
  max-height: calc(100vh - 320px); /* 调整最大高度 */
}

/* 移除不再需要的空状态样式 */

/* 扁平化布局样式 - 无滚动设计 */
.security-sections {
  display: flex;
  flex-direction: column;
  gap: 16px;
  flex: 1;
  overflow: hidden;
}

.mqtt-section {
  background: white;
  border-radius: 8px;
  border: 1px solid #e0e0e0;
  overflow: hidden;
  flex: 1;
  display: flex;
  flex-direction: column;
  min-height: 0;
}

.section-header {
  background: #f8f9fa;
  padding: 12px 16px;
  border-bottom: 1px solid #e0e0e0;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.section-header h4 {
  margin: 0;
  font-size: 14px;
  font-weight: 600;
  color: #333;
  display: flex;
  align-items: center;
  gap: 8px;
}

.mqtt-section .el-table {
  border-radius: 0;
  width: 100%;
}

.mqtt-section .el-table .el-table__body-wrapper {
  overflow-y: auto;
}

/* 确保表格适应容器 */
.standard-table {
  width: 100%;
}

.topic-table {
  width: 100%;
  margin-bottom: 0;
}

/* 移除重复的topic-search样式，使用统一的header-input */

/* 移除mqtt-section的空状态样式 */

/* 统一的标题栏按钮样式 */
.add-btn, .header-btn {
  padding: 6px 12px;
  border: 1px solid #409eff;
  border-radius: 4px;
  background: #409eff;
  color: white;
  cursor: pointer;
  font-size: 13px;
  height: 32px; /* 统一高度 */
  transition: all 0.2s;
  display: flex;
  align-items: center;
  gap: 4px;
  box-sizing: border-box;
}

.add-btn:hover, .header-btn:hover:not(:disabled) {
  background: #337ecc;
  border-color: #337ecc;
}

.add-btn i, .header-btn i {
  font-size: 12px;
}

/* 刷新按钮变体 */
.header-btn.refresh {
  background: #67c23a;
  border-color: #67c23a;
}

.header-btn.refresh:hover:not(:disabled) {
  background: #529b2e;
  border-color: #529b2e;
}

/* 危险按钮变体 */
.header-btn.danger {
  background: #f56c6c;
  border-color: #f56c6c;
}

.header-btn.danger:hover:not(:disabled) {
  background: #f04c4c;
  border-color: #f04c4c;
}

.header-btn:disabled {
  background: #c0c4cc;
  border-color: #c0c4cc;
  cursor: not-allowed;
  opacity: 0.6;
}

/* 标题栏操作区域 */
.header-actions {
  display: flex;
  gap: 8px;
  align-items: center;
}

/* 统一的标题栏输入框样式 */
.header-input .el-input__inner {
  height: 32px !important; /* 与按钮相同高度 */
  font-size: 13px !important;
  padding: 0 10px !important;
  line-height: 30px !important;
}

.header-input .el-input__prefix {
  height: 32px !important;
  line-height: 32px !important;
}

.header-input .el-input__suffix {
  height: 32px !important;
  line-height: 32px !important;
}

/* 标准表格样式 */
.standard-table {
  width: 100%;
  border-collapse: collapse;
  background: white;
  border-radius: 8px;
  overflow: hidden;
  border: 1px solid #e0e0e0;
}

.standard-table .el-table th {
  background: #f8f9fa;
  color: #333;
  font-weight: 600;
}

.standard-table .el-table td {
  color: #333;
}

/* 现代化选择器样式 - 合理尺寸 */
.form-select-modern {
  width: 100%;
}

.form-select-modern .el-input__inner {
  height: 32px !important; /* 合理高度 */
  font-size: 13px !important; /* 正常字体 */
  padding: 0 10px !important; /* 合理内边距 */
  line-height: 30px !important;
}

.select-prefix {
  font-size: 12px;
  color: #999;
  margin-right: 8px;
}

.client-option {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 4px 0;
}

.client-info {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.client-id {
  font-weight: 500;
  color: #333;
}

.client-username {
  font-size: 12px;
  color: #666;
}

.client-topics {
  font-size: 12px;
  color: #999;
}

/* 移除自定义下拉菜单样式，使用默认行为 */

/* 移除重复的topic-table样式 */

.topic-pattern {
  display: flex;
  align-items: center;
  gap: 8px;
}

.topic-pattern i {
  color: #409eff;
}

.topic-count {
  font-weight: bold;
  color: #409eff;
  font-size: 16px;
}

.text-muted {
  color: #999;
}





.specific-topic {
  background: white;
  border: 1px solid #e0e0e0;
  border-radius: 4px;
  padding: 8px 12px;
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 13px;
  color: #555;
}

.specific-topic i {
  color: #67c23a;
}

.specific-topic:hover {
  background: #f5f7fa;
  border-color: #409eff;
}

.topic-item i {
  color: #666;
  font-size: 16px;
}

/* 消息内容样式 */
.message-content {
  max-width: 250px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  color: #333;
}

/* 移除重复的清除按钮样式，使用统一的header-btn */


/* 现代对话框样式 */
.modern-dialog {
  max-height: 90vh;
  overflow-y: auto;
  margin-top: 2vh !important;
}

.modern-dialog .el-dialog__header {
  background: #f8f9fa;
  color: #374151;
  padding: 8px 16px;
  margin: 0;
  border-radius: 8px 8px 0 0;
  border-bottom: 1px solid #e5e7eb;
}

.modern-dialog .el-dialog__title {
  color: #374151;
  font-weight: 600;
  font-size: 16px;
}

.modern-dialog .el-dialog__headerbtn .el-dialog__close {
  color: #6b7280;
  font-size: 18px;
}

.modern-dialog .el-dialog__headerbtn .el-dialog__close:hover {
  color: #374151;
}

.modern-dialog .el-dialog__body {
  padding: 0;
  max-height: calc(90vh - 80px);
  overflow-y: auto;
}

/* 用户对话框和设备对话框特定样式 */
.modern-dialog .modern-form-container .modern-form .form-section,
.modern-dialog .device-tabs .modern-form .form-section {
  margin: 0 0 20px 0;
  border: 1px solid #e2e8f0;
  border-radius: 0;
  overflow: hidden;
  background: white;
  box-shadow: none;
}

.modern-dialog .modern-form-container .modern-form .form-section:last-child,
.modern-dialog .device-tabs .modern-form .form-section:last-child {
  margin-bottom: 0;
}

.modern-dialog .modern-form-container .modern-form .section-header,
.modern-dialog .device-tabs .modern-form .section-header {
  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
  padding: 12px 24px;
  border-bottom: 1px solid #e2e8f0;
  margin: 0;
}

.modern-dialog .modern-form-container .modern-form .section-title,
.modern-dialog .device-tabs .modern-form .section-title {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: #1e293b;
  display: flex;
  align-items: center;
  gap: 8px;
}

.modern-dialog .modern-form-container .modern-form .section-title i,
.modern-dialog .device-tabs .modern-form .section-title i {
  color: #3b82f6;
  font-size: 18px;
}

.modern-dialog .modern-form-container .modern-form .section-content,
.modern-dialog .device-tabs .modern-form .section-content {
  padding: 20px 24px;
}

/* 通用表单容器样式（用于设备对话框等） */
.modern-form-container {
  padding: 8px;
}

.modern-form .form-section {
  margin-bottom: 6px;
  border: 1px solid #e1e8ed;
  border-radius: 6px;
  overflow: hidden;
  background: white;
}

.modern-form .section-header {
  background: none;
  padding: 6px 12px;
  border-bottom: 1px solid #e1e8ed;
}

.modern-form .section-title {
  margin: 0;
  font-size: 14px;
  font-weight: 600;
  color: #495057;
  display: flex;
  align-items: center;
  gap: 6px;
}

.modern-form .section-title i {
  color: #667eea;
  font-size: 16px;
}

.modern-form .section-content {
  padding: 10px;
}

.modern-form .form-help {
  font-size: 12px;
  color: #6c757d;
  margin-top: 2px;
  line-height: 1.3;
}

.modern-form .el-form-item {
  margin-bottom: 10px;
}

.modern-form .el-form-item__label {
  color: #495057;
  font-weight: 500;
  font-size: 14px;
}

.modern-form .el-input__inner,
.modern-form .el-textarea__inner,
.modern-form .el-input-number .el-input__inner {
  border-color: #e1e8ed;
  border-radius: 6px;
  transition: all 0.2s ease;
}

.modern-form .el-input__inner:focus,
.modern-form .el-textarea__inner:focus,
.modern-form .el-input-number .el-input__inner:focus {
  border-color: #667eea;
  box-shadow: 0 0 0 2px rgba(102, 126, 234, 0.1);
}

.modern-form .limit-settings {
  background: #f8f9fa;
  border: 1px solid #e9ecef;
  border-radius: 6px;
  padding: 10px;
  margin-top: 6px;
}

.modern-form .usage-control {
  display: flex;
  align-items: center;
  gap: 8px;
}

.modern-form .reset-btn {
  padding: 4px 12px;
  font-size: 12px;
  border-radius: 4px;
  margin-left: 8px;
}

.modern-form .el-checkbox-group {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.modern-form .el-checkbox {
  margin-right: 0;
}

/* 对话框底部按钮样式 */
.dialog-footer {
  padding: 10px 16px;
  background: #f8f9fa;
  border-top: 1px solid #e1e8ed;
  text-align: right;
  margin: 0;
}

.dialog-footer .el-button {
  padding: 8px 20px;
  border-radius: 6px;
  font-weight: 500;
  display: inline-flex;
  align-items: center;
  gap: 6px;
}

.dialog-footer .el-button--primary {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border: none;
}

.dialog-footer .el-button--primary:hover {
  background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .modern-dialog {
    width: 95% !important;
    margin: 0 auto;
  }
  
  .modern-form-container {
    padding: 16px;
  }
  
  .modern-form .section-content {
    padding: 16px;
  }
}

/* 全局修复Element Plus下拉框滚动问题 */
.el-popper {
  position: fixed !important;
  z-index: 9999 !important;
}

.el-select-dropdown {
  position: fixed !important;
  z-index: 9999 !important;
}

/* 防止滚动时下拉框闪烁 */
.el-select.is-focused .el-input__inner {
  border-color: #409eff !important;
}

/* 强制所有弹出层使用固定定位 */
.el-popper.is-pure {
  position: fixed !important;
}

/* 修复快速滚动时的定位更新 */
.el-select .el-select-dropdown {
  transform: translateZ(0) !important;
  will-change: transform !important;
}


.text-success {
  color: #67c23a !important;
  font-weight: 600;
}

.text-danger {
  color: #f56c6c !important;
  font-weight: 600;
}

/* 移动端MQTT管理适配 */
@media (max-width: 768px) {
  .mqtt-management {
    width: 100%;
    height: calc(100vh - 120px); /* 固定高度，无滚动 */
  }
  
  .client-items, .topic-items {
    flex-direction: column;
    gap: 8px;
  }
  
  .client-item, .topic-item {
    width: 100%;
    justify-content: flex-start;
  }
  
  .publish-form .el-form {
    flex-direction: column;
  }
  
  .publish-form .el-form-item {
    margin-bottom: 15px;
    width: 100%;
  }
  
  .publish-form .el-form-item .el-input,
  .publish-form .el-form-item .el-select {
    width: 100% !important;
  }
  
  .publish-form .el-tabs__content {
    padding: 10px 0;
  }
}

.captcha-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.6);
  backdrop-filter: blur(5px);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.captcha-modal {
  background: white;
  border-radius: 12px;
  padding: 24px;
  max-width: 400px;
  width: 90%;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
  animation: captchaSlideIn 0.3s ease-out;
}

/* 移动端验证码弹窗适配 */
@media (max-width: 768px) {
  .captcha-modal {
    width: 95%;
    max-width: none;
    margin: 0 10px;
    padding: 20px;
  }
  
  .captcha-modal .captcha-image {
    width: 100%;
    max-width: 300px;
    height: auto;
  }
  
  .captcha-modal .captcha-actions {
    flex-direction: column;
    gap: 10px;
  }
  
  .captcha-modal .captcha-actions .el-button {
    width: 100%;
  }
}

@keyframes captchaSlideIn {
  from {
    transform: translateY(-20px);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

.captcha-header {
  text-align: center;
  margin-bottom: 20px;
}

.captcha-header h3 {
  margin: 0 0 8px 0;
  color: #333;
  font-size: 18px;
}

.captcha-subtitle {
  color: #666;
  font-size: 14px;
}

.captcha-content {
  text-align: center;
}

.captcha-loading {
  padding: 40px;
  color: #666;
  font-size: 14px;
}

.captcha-image-container {
  position: relative;
  display: inline-block;
  margin-bottom: 20px;
  border: 2px solid #e0e0e0;
  border-radius: 8px;
  overflow: hidden;
}

.captcha-image {
  display: block;
  width: 300px;
  height: 150px;
  cursor: crosshair;
}

.click-marker {
  position: absolute;
  width: 12px;
  height: 12px;
  background: #007bff;
  border-radius: 50%;
  border: 2px solid #fff;
  pointer-events: none;
  box-shadow: 0 0 4px rgba(0, 123, 255, 0.5);
  font-size: 0;
}

.captcha-actions {
  display: flex;
  justify-content: center;
  gap: 12px;
  margin-bottom: 16px;
}

.captcha-message {
  font-size: 14px;
  margin-top: 8px;
  padding: 8px;
  border-radius: 4px;
  background: #f5f5f5;
}

/* MQTT管理界面样式 */
.mqtt-tabs {
  margin-bottom: 10px; /* 减小下边距 */
  flex: 1;
  display: flex;
  flex-direction: column;
  /* 允许标签页整体滚动，移除overflow: hidden */
}

.mqtt-tabs {
  background: transparent;
  border: none;
}

.mqtt-tabs .el-tabs__header {
  margin-bottom: 16px;
}

.mqtt-tabs {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.mqtt-tabs .el-tabs__header {
  flex-shrink: 0;
  margin-bottom: 12px;
}

.mqtt-tabs .el-tabs__content {
  padding: 0;
  flex: 1;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.security-section {
  margin-bottom: 8px; /* 进一步减小下边距 */
}

.security-section h3 {
  margin-bottom: 6px; /* 进一步减小下边距 */
  color: #333;
  font-size: 1.05em; /* 略微减小字体 */
  border-bottom: 1px solid #e0e0e0; /* 减细边框 */
  padding-bottom: 4px; /* 进一步减小下内边距 */
}

.blacklist-toolbar {
  margin-bottom: 6px; /* 进一步减小下边距 */
  display: flex;
  justify-content: flex-start;
  align-items: center;
}

.blacklist-table, .failed-auth-table {
  background: white;
  border-radius: 6px;
  padding: 8px 10px; /* 进一步缩小内边距 */
  border: 1px solid #e0e0e0;
  margin-bottom: 8px; /* 进一步缩小下边距 */
}

.config-section {
  background: white;
  border-radius: 6px;
  padding: 8px 12px; /* 进一步缩小内边距 */
  border: 1px solid #e0e0e0;
}

.config-form {
  max-width: 600px;
}

.config-form .el-form-item {
  margin-bottom: 8px; /* 进一步减小表单项之间的间距 */
}

.config-form .el-form-item__label {
  font-weight: 500;
  color: #333;
}

.config-form .el-input-number {
  width: 100%;
}

/* 移动端MQTT管理适配 */
@media (max-width: 768px) {
  .mqtt-tabs .el-tabs__content {
    padding: 8px 0;
    flex: 1;
    overflow: hidden;
  }
  
  .blacklist-table, .failed-auth-table, .config-section {
    padding: 8px 10px; /* 移动端进一步缩小 */
    margin-bottom: 10px;
  }
  
  .blacklist-toolbar {
    flex-direction: column;
    align-items: stretch;
    gap: 10px;
  }
  
  .config-form {
    max-width: 100%;
  }
}


/* 保持消息管理样式 */
.retained-section {
  margin-bottom: 15px; /* 减小下边距 */
}

.retained-section h3 {
  margin-bottom: 10px; /* 减小下边距 */
  color: #333;
  font-size: 1.1em;
  border-bottom: 2px solid #e0e0e0;
  padding-bottom: 6px; /* 减小下内边距 */
}

.retained-toolbar {
  margin-bottom: 10px; /* 减小下边距 */
  display: flex;
  justify-content: flex-start;
  align-items: center;
  gap: 8px; /* 减小间距 */
}

.retained-table {
  background: white;
  border-radius: 6px;
  padding: 10px 12px; /* 缩小内边距 */
  border: 1px solid #e0e0e0;
  margin-bottom: 15px; /* 缩小下边距 */
}

.message-content {
  max-width: 250px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.no-retained {
  text-align: center;
  color: #999;
  padding: 20px; /* 减小空状态的内边距 */
}

.no-retained i {
  font-size: 24px;
  margin-bottom: 8px;
  display: block;
  color: #ccc;
}

/* 遗嘱消息管理样式 */
.will-section {
  margin-bottom: 8px; /* 进一步减小下边距 */
}

.will-section h3 {
  margin-bottom: 6px; /* 进一步减小下边距 */
  color: #333;
  font-size: 1.05em; /* 略微减小字体 */
  border-bottom: 1px solid #e0e0e0; /* 减细边框 */
  padding-bottom: 4px; /* 进一步减小下内边距 */
}

.will-toolbar {
  margin-bottom: 6px; /* 进一步减小下边距 */
  display: flex;
  justify-content: flex-start;
  align-items: center;
  gap: 6px; /* 进一步减小间距 */
}

.will-table {
  background: white;
  border-radius: 6px;
  padding: 8px 10px; /* 进一步缩小内边距 */
  border: 1px solid #e0e0e0;
  margin-bottom: 8px; /* 进一步缩小下边距 */
}

.no-will {
  text-align: center;
  color: #999;
  padding: 20px; /* 减小空状态的内边距 */
}

.no-will i {
  font-size: 24px;
  margin-bottom: 8px;
  display: block;
  color: #ccc;
}

/* 操作日志页面样式 */
.logs-management {
  width: 100%;
  /* 移除高度限制，让内容自然流动 */
  padding: 8px; /* 缩小内边距，参考MQTT管理的方法 */
  display: flex;
  flex-direction: column;
  gap: 8px; /* 添加统一间距 */
}

/* 优化日志管理中的统计卡片 */
.logs-management .stats-grid {
  margin-bottom: 8px; /* 缩小下边距 */
}

.logs-management .stat-card {
  padding: 10px; /* 缩小内边距 */
}

.logs-management .modern-card {
  margin-bottom: 8px !important; /* 覆盖内联样式 */
  padding: 8px 12px; /* 缩小内边距 */
}

/* 移动端操作日志优化 */
@media (max-width: 768px) {
  .logs-management {
    padding: 6px; /* 移动端进一步缩小 */
  }
  
  .logs-filters {
    padding: 6px 8px;
  }
  
  .filter-row {
    flex-direction: column;
    gap: 10px;
    align-items: stretch;
  }
  
  .filter-item {
    flex-direction: column;
    align-items: stretch;
    gap: 4px;
  }
  
  .filter-select, .filter-input {
    width: 100%;
  }
  
  .logs-table {
    margin-bottom: 6px;
  }
  
  .table-cell {
    padding: 6px 8px;
    font-size: 12px;
  }
  
  .logs-pagination {
    padding: 6px 8px;
    min-height: 36px;
  }
}

.logs-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px; /* 缩小下边距 */
  padding: 12px 15px 10px 15px; /* 缩小内边距 */
  border-bottom: 2px solid #e0e0e0;
  flex-shrink: 0;
}

.logs-header h2 {
  margin: 0;
  color: #333 !important;
  font-size: 1.5em;
}

.logs-actions {
  display: flex;
  gap: 10px;
}

/* 移除重复的refresh-btn和clear-btn样式，使用统一的header-btn */

.logs-stats {
  display: flex;
  gap: 20px; /* 缩小间距 */
  margin-bottom: 15px; /* 缩小下边距 */
  padding: 10px 12px; /* 缩小内边距 */
  background: #f8f9fa;
  border-radius: 6px; /* 缩小圆角 */
}

.stat-item {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.stat-label {
  font-size: 14px;
  color: #666 !important;
  margin-bottom: 5px;
}

.stat-value {
  font-size: 24px;
  font-weight: bold;
  color: #333 !important;
}

.quick-clear-actions {
  display: flex;
  gap: 6px;
  flex-wrap: wrap;
  margin-top: 5px;
}

.quick-clear-btn {
  padding: 4px 8px;
  font-size: 12px;
  border: 1px solid #ddd;
  border-radius: 4px;
  background: white;
  color: #666;
  cursor: pointer;
  transition: all 0.2s;
  white-space: nowrap;
}

.quick-clear-btn:hover {
  background: #f0f9ff;
  border-color: #409eff;
  color: #409eff;
}

.quick-clear-btn.danger {
  border-color: #f56c6c;
  color: #f56c6c;
}

.quick-clear-btn.danger:hover {
  background: #fef0f0;
  border-color: #f56c6c;
  color: #f56c6c;
}

.logs-filters {
  margin-bottom: 8px; /* 进一步缩小下边距 */
  padding: 8px 10px; /* 进一步缩小内边距 */
  background: white;
  border-radius: 6px;
  border: 1px solid #e0e0e0;
}

.filter-row {
  display: flex;
  gap: 20px;
  align-items: center;
  flex-wrap: wrap;
}

.filter-item {
  display: flex;
  align-items: center;
  gap: 8px;
}

.filter-item label {
  font-size: 14px;
  color: #666 !important;
  white-space: nowrap;
}

.filter-select, .filter-input {
  padding: 6px 12px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 14px;
  color: #333 !important;
  background: white;
}

.filter-select {
  min-width: 120px;
}

.filter-input {
  min-width: 200px;
}

.filter-btn {
  padding: 6px 16px;
  background: #409eff;
  color: white !important;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
  transition: background-color 0.2s;
}

.filter-btn:hover {
  background: #337ecc;
  color: white !important;
}

.logs-table {
  background: white;
  border-radius: 6px; /* 缩小圆角 */
  border: 1px solid #e0e0e0;
  overflow-x: auto;
  margin-bottom: 8px; /* 进一步缩小下边距 */
  flex: 1;
  display: flex;
  flex-direction: column;
}

.table-header {
  display: flex;
  background: #f8f9fa;
  border-bottom: 1px solid #e0e0e0;
  font-weight: bold;
  color: #333 !important;
}

.table-body {
  flex: 1;
  /* 移除overflow-y: auto，使用父容器滚动 */
  /* min-height: 200px; */ /* 移除固定高度限制 */
}

.table-row {
  display: flex;
  border-bottom: 1px solid #f0f0f0;
  transition: background-color 0.2s;
}

.table-row:hover {
  background: #f8f9fa;
}

.table-cell {
  padding: 8px 10px; /* 缩小内边距 */
  flex: 1;
  min-width: 100px;
  font-size: 13px; /* 略微缩小字体 */
  color: #333 !important;
  overflow: hidden;
  text-overflow: ellipsis;
  line-height: 1.3; /* 优化行高 */
}

.table-cell:first-child {
  min-width: 180px;
  white-space: nowrap;
}

.table-cell:nth-child(2) {
  min-width: 80px;
  white-space: nowrap;
}

.table-cell:nth-child(3) {
  min-width: 100px;
  white-space: nowrap;
}

.table-cell:nth-child(4) {
  min-width: 80px;
  white-space: nowrap;
}

.table-cell:nth-child(5) {
  min-width: 200px;
  max-width: 300px;
  word-break: break-word;
}

.table-cell:nth-child(6) {
  min-width: 60px;
  white-space: nowrap;
}

.table-cell:nth-child(7) {
  min-width: 120px;
  white-space: nowrap;
}

.operation-login {
  color: #409eff !important;
  font-weight: 500;
}

.operation-failed {
  color: #f56c6c !important;
  font-weight: 500;
}

.operation-manage {
  color: #67c23a !important;
  font-weight: 500;
}

.operation-other {
  color: #909399 !important;
  font-weight: 500;
}

.success {
  color: #67c23a !important;
  font-weight: 500;
}

.failed {
  color: #f56c6c !important;
  font-weight: 500;
}

.no-logs {
  text-align: center;
  padding: 40px;
  color: #999 !important;
}

.no-logs i {
  font-size: 48px;
  margin-bottom: 10px;
  display: block;
  color: #ccc !important;
}

.no-logs p {
  color: #999 !important;
  margin: 0;
}

.logs-pagination {
  display: flex !important;
  justify-content: center;
  align-items: center;
  padding: 8px 12px; /* 进一步缩小内边距 */
  margin-top: 8px; /* 进一步缩小上边距 */
  border-top: 1px solid #e0e0e0;
  background: #fafafa;
  min-height: 40px; /* 进一步缩小最小高度 */
  flex-shrink: 0;
}

.logs-pagination .el-pagination {
  display: flex;
  align-items: center;
  gap: 10px;
}

/* 搜索输入框样式 */
.search-input {
  padding: 8px 12px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 14px;
  width: 200px;
  margin-right: 10px;
}

.search-input:focus {
  outline: none;
  border-color: #409eff;
}

/* 分页组件样式 */
.user-pagination {
  display: flex !important;
  justify-content: center;
  align-items: center;
  padding: 12px 15px; /* 缩小内边距 */
  margin-top: 15px; /* 缩小上边距 */
  border-top: 1px solid #e0e0e0;
  background: #fafafa;
  min-height: 50px; /* 缩小最小高度 */
  flex-shrink: 0;
}

.user-pagination .el-pagination {
  display: flex;
  align-items: center;
  gap: 10px;
}

/* 移动端分页适配 */
@media (max-width: 768px) {
  .user-pagination {
    padding: 15px 10px;
    margin-top: 15px;
  }
  
  .user-pagination .el-pagination {
    flex-wrap: wrap;
    justify-content: center;
    gap: 8px;
  }
  
  .user-pagination .el-pagination .el-pager .number {
    min-width: 28px;
    height: 28px;
    line-height: 28px;
    font-size: 13px;
  }
  
  .user-pagination .el-pagination .btn-prev,
  .user-pagination .el-pagination .btn-next {
    width: 28px;
    height: 28px;
    line-height: 28px;
    font-size: 13px;
  }
}

/* 文本颜色类 */
.text-success {
  color: #67c23a !important;
  font-weight: 500;
}

.text-danger {
  color: #f56c6c !important;
  font-weight: 500;
}

.text-muted {
  color: #909399 !important;
}

/* 表单帮助文本 */
.form-help {
  font-size: 12px;
  color: #909399;
  margin-top: 4px;
  line-height: 1.4;
}

/* 客户端ID列样式 - 小字体，防止换行 */
.client-id-cell {
  font-size: 12px !important;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace !important;
  white-space: nowrap !important;
  overflow: hidden !important;
  text-overflow: ellipsis !important;
}

.client-id-cell .cell {
  font-size: 12px !important;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace !important;
  white-space: nowrap !important;
  overflow: hidden !important;
  text-overflow: ellipsis !important;
  padding: 8px 12px !important;
}

/* 遗嘱消息表格优化样式 */
.will-messages-table {
  font-size: 13px !important;
}

.will-messages-table .el-table__header th {
  font-size: 13px !important;
  padding: 8px 6px !important;
}

.will-messages-table .el-table__body td {
  font-size: 13px !important;
  padding: 8px 6px !important;
}

/* 遗嘱消息紧凑列样式 */
.will-compact-cell {
  font-size: 12px !important;
  text-align: center !important;
}

.will-compact-cell .cell {
  font-size: 12px !important;
  padding: 6px 4px !important;
  text-align: center !important;
}

/* 遗嘱主题列样式 */
.will-topic-cell {
  font-size: 12px !important;
}

.will-topic-cell .cell {
  font-size: 12px !important;
  padding: 6px 8px !important;
  white-space: nowrap !important;
  overflow: hidden !important;
  text-overflow: ellipsis !important;
}

/* 遗嘱消息列样式 */
.will-message-cell {
  font-size: 12px !important;
}

.will-message-cell .cell {
  font-size: 12px !important;
  padding: 6px 8px !important;
}

.will-message-content {
  max-width: 180px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  font-size: 12px;
  line-height: 1.4;
}

/* 遗嘱日期显示优化 */
.will-date-cell {
  font-size: 11px;
  line-height: 1.3;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  cursor: help;
  max-width: 140px;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
}

/* 遗嘱操作列样式 */
.will-action-cell {
  text-align: center !important;
}

.will-action-cell .cell {
  padding: 6px 4px !important;
  text-align: center !important;
  overflow: visible !important;
}

.will-action-btn {
  font-size: 12px !important;
  padding: 4px 8px !important;
  min-width: 50px !important;
  height: 24px !important;
  line-height: 1 !important;
}

/* MQTT配置界面优化样式 - 紧凑版 */
.mqtt-config-container {
  padding: 10px;
  /* Removed height restrictions and overflow to use parent scrolling */
}

.config-info-banner {
  background: #e8f4fd;
  border: 1px solid #b3d9ff;
  border-radius: 6px;
  padding: 10px 15px;
  margin-bottom: 15px;
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 14px;
  color: #0066cc;
}

.config-info-banner i {
  font-size: 16px;
  color: #0066cc;
}

.config-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 15px; /* 减小网格间距 */
  margin-bottom: 15px; /* 减小下边距 */
}

.config-column {
  background: white;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  overflow: hidden;
}

.config-group {
  height: 100%;
}

.config-group-title {
  background-color: var(--color-bg-tertiary);
  color: #495057;
  padding: 10px 15px;
  margin: 0;
  font-size: 14px;
  font-weight: 600;
  border-bottom: 1px solid #e0e0e0;
  display: flex;
  align-items: center;
  gap: 8px;
}

.config-group-title i {
  font-size: 14px;
}

.config-form-compact {
  display: contents;
}

.config-form-compact .config-column .el-form-item {
  margin: 15px;
  margin-bottom: 12px;
}

.config-form-compact .el-form-item__label {
  font-size: 13px;
  color: #606266;
  font-weight: 500;
}

.input-with-unit-compact {
  display: flex;
  align-items: center;
  gap: 8px;
}

.input-with-unit-compact .el-input-number {
  flex: 1;
}

.input-with-unit-compact .unit {
  color: #909399;
  font-size: 12px;
  font-weight: 500;
  min-width: 30px;
}

.timing-preview-compact {
  background: #f0f9ff;
  border: 1px solid #bfdbfe;
  border-radius: 4px;
  padding: 10px;
  margin: 10px 15px 15px 15px;
}

.timing-title-compact {
  display: flex;
  align-items: center;
  gap: 6px;
  font-weight: 600;
  color: #1e40af;
  margin-bottom: 8px;
  font-size: 12px;
}

.timing-title-compact i {
  font-size: 12px;
}

.timing-items {
  display: flex;
  gap: 15px;
}

.timing-item-compact {
  font-size: 12px;
  color: #374151;
  background: white;
  padding: 4px 8px;
  border-radius: 3px;
  border: 1px solid #bfdbfe;
}

.config-actions-compact {
  padding: 10px 15px; /* 缩小内边距 */
  border-top: 1px solid #e0e0e0;
  background: #f8f9fa;
  display: flex;
  gap: 8px; /* 缩小间距 */
  justify-content: center;
}

.config-actions-compact .el-button {
  min-width: 100px;
}

/* 移动端MQTT配置适配 */
@media (max-width: 768px) {
  .mqtt-config-container {
    padding: 8px;
    max-height: calc(100vh - 180px); /* 改为max-height */
  }
  
  .config-grid {
    grid-template-columns: 1fr;
    gap: 12px; /* 移动端减小间距 */
  }
  
  .config-form-compact .config-column .el-form-item {
    margin: 12px;
    margin-bottom: 10px;
  }
  
  .config-group-title {
    padding: 8px 12px;
    font-size: 13px;
  }
  
  .timing-items {
    flex-direction: column;
    gap: 8px;
  }
  
  .config-actions-compact {
    padding: 8px 12px; /* 移动端进一步缩小 */
    flex-direction: column;
    gap: 6px; /* 缩小间距 */
  }
  
  .config-actions-compact .el-button {
    width: 100%;
  }
}

/* 消息发送网格布局 - 合理无滚动设计 */
.publish-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 12px;
  height: 100%;
  overflow: hidden;
  align-content: start;
  max-height: calc(100vh - 260px);
}

/* 发布表单紧凑样式 */
.publish-form-compact {
  display: flex;
  flex-direction: column;
  gap: 10px; /* 合理间距 */
  height: 100%;
  overflow: hidden;
}

.form-row {
  display: flex;
  flex-direction: column;
  gap: 6px; /* 合理间距 */
  margin-bottom: 8px;
}

.form-row-inline {
  display: flex;
  gap: 12px; /* 合理间距 */
  align-items: end;
  margin-bottom: 8px;
}

.form-col {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 5px;
}

.form-label {
  font-size: 13px; /* 恢复正常字体 */
  color: #606266;
  font-weight: 500;
  margin: 0;
  line-height: 1.4;
}

.form-label-checkbox {
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 13px;
  color: #606266;
  cursor: pointer;
  margin: 0;
  margin-top: 20px;
}

.form-input-compact {
  padding: 6px 10px; /* 合理的内边距 */
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  font-size: 13px; /* 正常字体 */
  height: 32px; /* 合理高度 */
  transition: border-color 0.2s;
  background: white;
  outline: none;
  box-sizing: border-box;
}

.form-input-compact:focus {
  border-color: #409eff;
  box-shadow: 0 0 0 2px rgba(64, 158, 255, 0.2);
}

.form-input-compact::placeholder {
  color: #c0c4cc;
  font-size: 13px;
}

.form-select-compact {
  padding: 8px 12px;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  font-size: 13px;
  background: white;
  outline: none;
  cursor: pointer;
  transition: border-color 0.2s;
}

.form-select-compact:focus {
  border-color: #409eff;
  box-shadow: 0 0 0 2px rgba(64, 158, 255, 0.2);
}

.form-checkbox-compact {
  width: 14px;
  height: 14px;
  cursor: pointer;
  accent-color: #409eff;
}

.form-actions {
  display: flex;
  justify-content: flex-start;
  margin-top: 10px;
}

.publish-btn-compact, .send-btn-compact {
  padding: 8px 16px; /* 合理内边距 */
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 13px; /* 正常字体 */
  font-weight: 500;
  height: 32px; /* 合理高度 */
  transition: all 0.2s;
  display: flex;
  align-items: center;
  gap: 6px;
  min-width: 120px;
  justify-content: center;
}

.publish-btn-compact {
  background: #409eff;
  color: white;
}

.publish-btn-compact:hover:not(:disabled) {
  background: #337ecc;
  transform: translateY(-1px);
}

.publish-btn-compact:disabled {
  background: #a0cfff;
  cursor: not-allowed;
  transform: none;
}

.send-btn-compact {
  background: #67c23a;
  color: white;
}

.send-btn-compact:hover:not(:disabled) {
  background: #529b2e;
  transform: translateY(-1px);
}

.send-btn-compact:disabled {
  background: #a4da7a;
  cursor: not-allowed;
  transform: none;
}

.publish-btn-compact i, .send-btn-compact i {
  font-size: 14px;
}

/* 移动端发布表单适配 */
@media (max-width: 768px) {
  .publish-grid {
    grid-template-columns: 1fr;
    gap: 12px;
    align-content: start;
    max-height: calc(100vh - 200px);
  }

  .form-row-inline {
    flex-direction: column;
    gap: 10px;
    align-items: stretch;
  }

  .form-label-checkbox {
    margin-top: 0;
  }

  .publish-form-compact {
    gap: 12px;
  }

  .form-actions {
    justify-content: stretch;
  }

  .publish-btn-compact, .send-btn-compact {
    width: 100%;
    min-width: auto;
  }
}

/* 用户对话框样式 */
.user-dialog {
  max-height: 90vh;
}

.user-dialog .el-dialog__body {
  max-height: 70vh;
  overflow-y: auto;
  padding: 20px;
}

.user-tabs {
  min-height: 400px;
}

.user-tabs .el-tab-pane {
  padding-top: 20px;
}

/* 响应式样式 */
@media (max-width: 768px) {
  .user-dialog .el-dialog__body {
    max-height: 75vh;
    padding: 15px;
  }
  
  .user-tabs {
    min-height: 350px;
  }
  
  .user-tabs .el-form-item {
    margin-bottom: 16px;
  }
  
  .user-tabs .el-form-item__label {
    font-size: 14px;
  }
}

@media (max-width: 480px) {
  .user-dialog .el-dialog__body {
    max-height: 80vh;
    padding: 10px;
  }
  
  .user-tabs {
    min-height: 300px;
  }
  
  .user-tabs .el-form-item {
    margin-bottom: 12px;
  }
}

/* MQTT统计卡片优化样式 - 无滚动设计 */
.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));
  gap: 8px;
  margin-bottom: 12px;
  flex-shrink: 0; /* 防止被压缩 */
  height: auto; /* 自适应高度 */
}

.stat-card {
  background: rgba(255, 255, 255, 0.95);
  border: 1px solid rgba(102, 126, 234, 0.1);
  border-radius: 8px; /* 减小圆角 */
  padding: 12px; /* 减小内边距 */
  text-align: center;
  transition: all 0.3s ease;
  box-shadow: 0 2px 8px rgba(0,0,0,0.06); /* 减小阴影 */
  backdrop-filter: blur(10px);
  position: relative;
  overflow: hidden;
  min-height: 80px; /* 设置最小高度，让卡片更紧凑 */
}

.stat-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 3px;
  background: linear-gradient(90deg, #667eea, #764ba2);
  opacity: 0;
  transition: opacity 0.3s ease;
}

.stat-card:hover::before {
  opacity: 1;
}

.stat-card:hover {
  border-color: rgba(102, 126, 234, 0.3);
  box-shadow: 0 8px 25px rgba(102, 126, 234, 0.15);
  transform: translateY(-2px);
}

.stat-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 4px; /* 进一步减小间距 */
}

.stat-label {
  font-size: 11px; /* 进一步减小字体 */
  color: #666;
  font-weight: 500;
}

.stat-icon {
  width: 28px; /* 减小图标尺寸 */
  height: 28px; /* 减小图标尺寸 */
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px; /* 减小图标字体 */
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
  transition: all 0.3s ease;
}

.stat-card:hover .stat-icon {
  transform: scale(1.1);
  box-shadow: 0 4px 12px rgba(0,0,0,0.15);
}

/* 统计图标颜色已在非scoped样式块中定义 */

.stat-value {
  font-size: 18px; /* 进一步减小数值字体 */
  font-weight: bold;
  color: #333;
  line-height: 1.1; /* 减小行高 */
  margin-top: 2px; /* 减小上边距 */
}

/* 现代卡片样式优化 */
.modern-card {
  background: white;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 1px 3px rgba(0,0,0,0.1);
}

.card-header {
  background: #f8f9fa;
  padding: 12px 16px; /* 减小内边距 */
  border-bottom: 1px solid #e0e0e0;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.card-title {
  margin: 0;
  font-size: 14px; /* 减小标题字体 */
  font-weight: 600;
  color: #333;
  display: flex;
  align-items: center;
  gap: 8px;
}

.card-title i {
  color: #409eff;
  font-size: 14px; /* 减小图标 */
}

/* MQTT管理布局优化 */
.mqtt-management {
  width: 100%;
  min-height: calc(100vh - 140px);
  display: flex;
  flex-direction: column;
  overflow-y: auto !important; /* 强制启用垂直滚动 */
}

.mqtt-toolbar {
  margin-bottom: 8px; /* 减小工具栏下边距 */
}

.mqtt-tab-header {
  margin-bottom: 8px; /* 减小标签页头部下边距 */
}

/* 移除不再需要的特殊处理 */

/* MQTT表格优化样式 */
.standard-table .el-table {
  font-size: 13px; /* 减小表格字体 */
}

.standard-table .el-table th {
  background: #f8f9fa;
  color: #333;
  font-weight: 600;
  padding: 8px 12px; /* 减小表头padding */
  font-size: 12px; /* 减小表头字体 */
}

.standard-table .el-table td {
  color: #333;
  padding: 6px 12px; /* 减小单元格padding */
}

.standard-table .el-table .cell {
  line-height: 1.3; /* 减小行高 */
}

/* 专门针对MQTT表格的优化 */
.mqtt-management .el-table .el-table__row {
  height: auto; /* 允许行高自适应 */
}

.mqtt-management .el-table .cell {
  padding: 4px 8px; /* 进一步减小MQTT表格单元格内边距 */
  line-height: 1.2;
}

.mqtt-management .el-tag {
  padding: 2px 6px; /* 减小标签内边距 */
  font-size: 11px; /* 减小标签字体 */
  height: auto;
  line-height: 1.2;
}

.mqtt-management .el-button--small {
  padding: 4px 8px; /* 减小小按钮内边距 */
  font-size: 11px;
  height: auto;
  line-height: 1.2;
}

/* 客户端ID单元格特殊样式 */
.client-id-cell .cell {
  font-family: 'Monaco', 'Menlo', monospace;
  font-size: 11px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

/* 现代表格容器 */
.modern-table-container {
  background: white;
  border-radius: 8px;
  border: 1px solid #e0e0e0;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  margin-top: 20px;
}

/* 现代表格样式 */
.modern-table {
  width: 100%;
  border-collapse: collapse;
  font-size: 14px;
  line-height: 1.5;
}

.modern-table thead {
  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
}

.modern-table th {
  padding: 16px 12px;
  text-align: left;
  font-weight: 600;
  color: #374151;
  border-bottom: 2px solid #e5e7eb;
  font-size: 13px;
  text-transform: uppercase;
  letter-spacing: 0.05em;
  white-space: nowrap;
}

.modern-table tbody tr {
  transition: all 0.2s ease;
  border-bottom: 1px solid #f3f4f6;
}

.modern-table tbody tr:hover {
  background-color: rgba(59, 130, 246, 0.04);
}

.modern-table td {
  padding: 14px 12px;
  vertical-align: middle;
  color: #374151;
  font-size: 14px;
}

/* 设备ID样式 */
.device-id {
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 12px;
  background: #f3f4f6;
  padding: 4px 8px;
  border-radius: 4px;
  color: #6b7280;
  border: 1px solid #e5e7eb;
  display: inline-block;
}

/* 设备名称样式 */
.device-name {
  display: flex;
  align-items: center;
  gap: 8px;
  font-weight: 500;
  color: #374151;
}

.device-name i {
  color: #3b82f6;
  font-size: 14px;
}

/* 用户信息样式 */
.user-info {
  display: flex;
  align-items: center;
  gap: 6px;
  color: #6b7280;
  font-size: 13px;
}

.user-info i {
  color: #9ca3af;
  font-size: 12px;
}

/* 设备类型徽章 */
.device-type-badge {
  display: inline-flex;
  align-items: center;
  padding: 4px 10px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 500;
  text-transform: capitalize;
}

/* 设备类型图标（仅图标模式） */
.device-type-icon-only {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  border-radius: 50%;
  background: rgba(0, 0, 0, 0.05);
  cursor: help;
  transition: all 0.2s ease;
  margin: 0 auto;
}

.device-type-icon-only:hover {
  background: rgba(0, 0, 0, 0.1);
  transform: scale(1.1);
}

.device-type-icon-only i {
  font-size: 16px;
  line-height: 1;
}

.device-type-badge.type-terminal {
  background: #dbeafe;
  color: #1d4ed8;
  border: 1px solid #bfdbfe;
}

.device-type-badge.type-mobile {
  background: #dcfce7;
  color: #166534;
  border: 1px solid #bbf7d0;
}

.device-type-badge.type-desktop {
  background: #fef3c7;
  color: #92400e;
  border: 1px solid #fde68a;
}

/* 授权状态徽章 */
.auth-badge {
  display: inline-flex;
  align-items: center;
  padding: 4px 10px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 500;
}

.auth-badge.auth-active {
  background: #dcfce7;
  color: #166534;
  border: 1px solid #bbf7d0;
}

.auth-badge.auth-expired {
  background: #fee2e2;
  color: #dc2626;
  border: 1px solid #fecaca;
}

.auth-badge.auth-disabled {
  background: #f3f4f6;
  color: #6b7280;
  border: 1px solid #e5e7eb;
}

.auth-badge.auth-limited {
  background: #fef3c7;
  color: #92400e;
  border: 1px solid #fde68a;
}

/* 使用统计样式 */
.usage-stats {
  display: flex;
  flex-direction: column;
  gap: 4px;
  min-width: 100px;
}

.usage-bar {
  width: 100%;
  height: 6px;
  background: #f3f4f6;
  border-radius: 3px;
  overflow: hidden;
  border: 1px solid #e5e7eb;
}

.usage-fill {
  height: 100%;
  background: linear-gradient(90deg, #10b981, #059669);
  border-radius: 3px;
  transition: width 0.3s ease;
}

.usage-fill.usage-warning {
  background: linear-gradient(90deg, #f59e0b, #d97706);
}

.usage-text {
  font-size: 11px;
  color: #6b7280;
  font-weight: 500;
  text-align: center;
}

/* 状态徽章 */
.status-badge {
  display: inline-flex;
  align-items: center;
  padding: 4px 10px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 500;
}

.status-badge.status-active {
  background: #dcfce7;
  color: #166534;
  border: 1px solid #bbf7d0;
}

.status-badge.status-inactive {
  background: #fee2e2;
  color: #dc2626;
  border: 1px solid #fecaca;
}

/* 操作按钮组 */
.action-buttons {
  display: flex;
  gap: 6px;
  align-items: center;
  justify-content: flex-start;
  flex-wrap: wrap;
}

.action-buttons .modern-btn {
  padding: 6px 10px;
  border-radius: 6px;
  font-size: 12px;
  font-weight: 500;
  border: none;
  cursor: pointer;
  transition: all 0.2s ease;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  min-width: 32px;
  height: 32px;
}

.action-buttons .modern-btn i {
  font-size: 12px;
}

.action-buttons .modern-btn.btn-primary {
  background: #3b82f6;
  color: white;
}

.action-buttons .modern-btn.btn-primary:hover {
  background: #2563eb;
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(59, 130, 246, 0.3);
}

.action-buttons .modern-btn.btn-warning {
  background: #f59e0b;
  color: white;
}

.action-buttons .modern-btn.btn-warning:hover {
  background: #d97706;
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(245, 158, 11, 0.3);
}

.action-buttons .modern-btn.btn-info {
  background: #06b6d4;
  color: white;
}

.action-buttons .modern-btn.btn-info:hover {
  background: #0891b2;
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(6, 182, 212, 0.3);
}

.action-buttons .modern-btn.btn-danger {
  background: #ef4444;
  color: white;
}

.action-buttons .modern-btn.btn-danger:hover {
  background: #dc2626;
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(239, 68, 68, 0.3);
}

/* 复选框样式 */
.modern-checkbox {
  width: 16px;
  height: 16px;
  border: 2px solid #d1d5db;
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.modern-checkbox:checked {
  background: #3b82f6;
  border-color: #3b82f6;
}

.modern-checkbox:hover {
  border-color: #3b82f6;
}

/* 设备统计卡片样式 */
.device-stats-cards {
  display: flex;
  gap: 16px;
  margin-bottom: 24px;
}

/* 设备列表标题样式 */
.section-header {
  margin-bottom: 16px;
}

.section-title {
  font-size: 18px;
  font-weight: 600;
  color: #374151;
  margin: 0;
  display: flex;
  align-items: center;
  gap: 8px;
}

.section-title i {
  color: #6b7280;
  font-size: 16px;
}

.device-stats-cards .stat-card {
  background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
  border-radius: 12px;
  padding: 20px;
  display: flex;
  align-items: center;
  gap: 16px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  border: 1px solid #e5e7eb;
  transition: all 0.3s ease;
  flex: 1;
  min-width: 200px;
}

.device-stats-cards .stat-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
}

.device-stats-cards .stat-icon {
  width: 48px;
  height: 48px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 20px;
  color: white;
  flex-shrink: 0;
}

.device-stats-cards .stat-icon.primary {
  background: linear-gradient(135deg, #3b82f6, #2563eb);
}

.device-stats-cards .stat-icon.success {
  background: linear-gradient(135deg, #10b981, #059669);
}

.device-stats-cards .stat-icon.danger {
  background: linear-gradient(135deg, #ef4444, #dc2626);
}

.device-stats-cards .stat-content {
  flex: 1;
}

.device-stats-cards .stat-value {
  font-size: 24px;
  font-weight: 700;
  color: #374151;
  line-height: 1;
  margin-bottom: 4px;
}

.device-stats-cards .stat-label {
  font-size: 13px;
  color: #6b7280;
  font-weight: 500;
}

/* 设备工具栏样式 */
.device-toolbar {
  background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
  border-radius: 12px;
  padding: 16px 20px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
  border: 1px solid #e5e7eb;
  margin-bottom: 16px;
}

.toolbar-buttons {
  display: flex;
  align-items: center;
  gap: 12px;
}

.toolbar-actions {
  display: flex;
  gap: 8px;
  align-items: center;
}

/* 批量操作栏样式 */
.batch-operations-bar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 24px;
  background: linear-gradient(135deg, #eff6ff 0%, #dbeafe 100%);
  border-top: 1px solid #bfdbfe;
  border-bottom: 1px solid #e5e7eb;
}

.batch-info {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 14px;
  font-weight: 500;
  color: #1d4ed8;
}

.batch-info i {
  color: #3b82f6;
}

.batch-actions {
  display: flex;
  gap: 8px;
  align-items: center;
}

/* 表格响应式设计 */
@media (max-width: 1200px) {
  .modern-table th,
  .modern-table td {
    padding: 12px 8px;
    font-size: 13px;
  }

  .action-buttons {
    gap: 4px;
  }

  .action-buttons .modern-btn {
    min-width: 28px;
    height: 28px;
    padding: 4px 6px;
  }

  .device-stats-cards {
    grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));
    gap: 12px;
    margin-bottom: 16px;
  }

  .device-stats-cards .stat-card {
    padding: 16px;
    gap: 12px;
  }

  .device-stats-cards .stat-icon {
    width: 40px;
    height: 40px;
    font-size: 18px;
  }

  .device-stats-cards .stat-value {
    font-size: 20px;
  }

  .device-toolbar {
    flex-direction: column;
    align-items: stretch;
    gap: 12px;
    padding: 12px 16px;
  }

  .toolbar-right {
    align-items: stretch;
    flex-wrap: wrap;
  }
}

@media (max-width: 768px) {
  .modern-table-container {
    overflow-x: auto;
    border-radius: 6px;
  }

  .modern-table {
    min-width: 800px;
    font-size: 12px;
  }

  .modern-table th,
  .modern-table td {
    padding: 10px 6px;
  }

  .device-name {
    flex-direction: column;
    gap: 4px;
    align-items: flex-start;
  }

  .usage-stats {
    min-width: 80px;
  }

  .action-buttons {
    flex-direction: column;
    gap: 2px;
  }

  .action-buttons .modern-btn {
    width: 100%;
    min-width: auto;
    height: 24px;
    font-size: 11px;
  }

  .device-stats-cards {
    grid-template-columns: 1fr;
    gap: 8px;
    margin-bottom: 12px;
  }

  .device-stats-cards .stat-card {
    padding: 12px;
    gap: 10px;
  }

  .device-stats-cards .stat-icon {
    width: 36px;
    height: 36px;
    font-size: 16px;
  }

  .device-stats-cards .stat-value {
    font-size: 18px;
  }

  .device-toolbar {
    padding: 10px 12px;
    gap: 8px;
  }

  .toolbar-left .toolbar-title {
    font-size: 14px;
  }

  .toolbar-right {
    gap: 6px;
    flex-wrap: wrap;
  }

  .batch-operations-bar {
    flex-direction: column;
    gap: 12px;
    align-items: stretch;
    padding: 12px 16px;
  }

  .batch-actions {
    justify-content: center;
    flex-wrap: wrap;
    gap: 6px;
  }
}

/* 加载和空状态样式 */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px 20px;
  color: #6b7280;
  background: #ffffff;
  border-radius: 8px;
  border: 1px solid #e5e7eb;
  margin-top: 20px;
}

.loading-spinner {
  width: 32px;
  height: 32px;
  border: 3px solid #f3f4f6;
  border-top: 3px solid #3b82f6;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 16px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 移除复杂的空状态样式，使用简单的文字显示 */

/* 改进的按钮尺寸 */
.modern-btn.btn-sm {
  padding: 6px 12px;
  font-size: 13px;
  height: 32px;
  min-width: auto;
}

.modern-btn.btn-xs {
  padding: 4px 8px;
  font-size: 12px;
  height: 24px;
  min-width: auto;
}

/* 文本颜色工具类 */
.text-success {
  color: #10b981 !important;
}

.text-danger {
  color: #ef4444 !important;
}

.text-warning {
  color: #f59e0b !important;
}

.text-info {
  color: #06b6d4 !important;
}

.text-muted {
  color: #6b7280 !important;
}

/* 设备对话框标签页样式 */
.device-tabs {
  margin: 0;
  flex: 1;
  display: flex;
  flex-direction: column;
}

.device-tabs .el-tabs__header {
  margin: 0 0 4px 0;
  padding: 0;
  flex-shrink: 0;
}

.device-tabs .el-tabs__nav-wrap {
  padding: 0 !important;
  margin-left: 0 !important;
}

.device-tabs .el-tabs__nav {
  margin-left: 0 !important;
  padding-left: 0 !important;
  transform: translateX(0) !important;
}

.device-tabs .el-tabs__item {
  margin-left: 0 !important;
  padding-left: 16px !important;
}

.device-tabs .el-tabs__nav-wrap {
  padding: 0;
}

.device-tabs .el-tabs__nav {
  /* 保持Element Plus默认的标签页布局 */
}

.device-tabs .el-tabs__content {
  padding: 0;
  flex: 1;
  display: flex;
  flex-direction: column;
}

.device-tabs .el-tab-pane {
  flex: 1;
  display: flex;
  flex-direction: column;
}

/* 精致商务风格设备对话框关闭按钮 */
.modern-dialog .el-dialog__header {
  position: relative;
  padding: 16px 24px 12px;
  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
  border-bottom: 1px solid #e2e8f0;
}

.modern-dialog .el-dialog__title {
  font-size: 18px;
  font-weight: 600;
  color: #1e293b;
  line-height: 1.3;
  letter-spacing: -0.025em;
}

.modern-dialog .el-dialog__headerbtn {
  position: absolute;
  top: 12px;
  right: 16px;
  width: 28px;
  height: 28px;
  background: #ffffff;
  border: 1px solid #e2e8f0;
  border-radius: 6px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.modern-dialog .el-dialog__headerbtn:hover {
  background: #f8fafc;
  border-color: #cbd5e1;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  transform: translateY(-1px);
}

.modern-dialog .el-dialog__headerbtn:active {
  transform: translateY(0);
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.modern-dialog .el-dialog__close {
  font-size: 14px;
  color: #64748b;
  font-weight: 400;
  transition: all 0.2s ease;
  line-height: 1;
}

.modern-dialog .el-dialog__headerbtn:hover .el-dialog__close {
  color: #374151;
}

.modern-dialog .el-dialog__body {
  padding: 0;
  background: #ffffff;
  display: flex;
  flex-direction: column;
  justify-content: center;
  min-height: 400px;
}

/* 用户对话框样式 */
.modern-dialog .modern-form-container {
  padding: 20px 0;
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: center;
}

/* 设备对话框样式 */
.modern-dialog .device-tabs {
  padding: 0 0 20px 0;
}

/* 表单帮助文本样式 */
.form-help {
  font-size: 12px;
  color: #6b7280;
  margin-top: 4px;
  line-height: 1.4;
}

.form-help.important {
  color: #f59e0b;
  font-weight: 500;
}

/* 自动创建设备徽章样式 */
.auto-created-badge {
  display: inline-flex;
  align-items: center;
  gap: 4px;
  padding: 2px 6px;
  margin-left: 8px;
  background: linear-gradient(135deg, #8b5cf6, #7c3aed);
  color: white;
  font-size: 10px;
  font-weight: 500;
  border-radius: 8px;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  box-shadow: 0 1px 3px rgba(139, 92, 246, 0.3);
}

.auto-created-badge i {
  font-size: 9px;
}

/* 设备类型管理样式 */
.device-sub-tabs {
  margin-bottom: 20px;
}

.device-sub-tabs .el-tabs__header {
  margin-bottom: 20px;
}

.device-types-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 20px;
  padding: 20px 0;
}

.device-type-card {
  background: white;
  border: 1px solid #e5e7eb;
  border-radius: 12px;
  padding: 20px;
  transition: all 0.3s ease;
  position: relative;
}

.device-type-card:hover {
  border-color: #3b82f6;
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.15);
  transform: translateY(-2px);
}

.device-type-card.system-type {
  border-left: 4px solid #10b981;
}

.device-type-card.inactive {
  opacity: 0.6;
  background: #f9fafb;
}

.type-header {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 12px;
}

.type-icon {
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(59, 130, 246, 0.1);
  border-radius: 8px;
  font-size: 18px;
}

.type-info {
  flex: 1;
}

.type-name {
  font-size: 16px;
  font-weight: 600;
  color: #1f2937;
  margin: 0 0 4px 0;
}

.type-code {
  font-size: 12px;
  color: #6b7280;
  font-family: 'Courier New', monospace;
  background: #f3f4f6;
  padding: 2px 6px;
  border-radius: 4px;
}

.type-status {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.system-badge {
  background: #10b981;
  color: white;
  font-size: 10px;
  padding: 2px 6px;
  border-radius: 4px;
  text-align: center;
}

.system-type-hint {
  font-size: 12px;
  color: #10b981;
  font-weight: 500;
  display: flex;
  align-items: center;
  gap: 4px;
}

.system-type-hint::before {
  content: "🔒";
  font-size: 10px;
}

.inactive-badge {
  background: #ef4444;
  color: white;
  font-size: 10px;
  padding: 2px 6px;
  border-radius: 4px;
  text-align: center;
}

.type-description {
  color: #6b7280;
  font-size: 14px;
  line-height: 1.5;
  margin-bottom: 16px;
  min-height: 42px;
}

.type-actions {
  display: flex;
  gap: 8px;
  justify-content: flex-end;
}

.device-type-form .form-help {
  font-size: 12px;
  color: #6b7280;
  margin-top: 4px;
}

.type-preview {
  margin-top: 20px;
  padding: 16px;
  background: #f8fafc;
  border-radius: 8px;
  border: 1px solid #e5e7eb;
}

.type-preview h4 {
  margin: 0 0 12px 0;
  font-size: 14px;
  color: #374151;
}

.preview-item {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 14px;
}

.preview-item i {
  font-size: 16px;
}

/* 图标选择器样式 */
.icon-selector {
  position: relative;
}

.selected-icon {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 12px;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  background: white;
  cursor: pointer;
  transition: all 0.2s ease;
}

.selected-icon:hover {
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.selected-icon i:first-child {
  font-size: 16px;
}

.selected-icon span {
  flex: 1;
  font-size: 14px;
  color: #374151;
}

.selected-icon i:last-child {
  font-size: 12px;
  color: #6b7280;
  transition: transform 0.2s ease;
}

.icon-selector.open .selected-icon i:last-child {
  transform: rotate(180deg);
}

.icon-dropdown {
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  z-index: 1000;
  background: white;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
  max-height: 300px;
  overflow: hidden;
}

.icon-search {
  padding: 12px;
  border-bottom: 1px solid #e5e7eb;
}

.icon-grid {
  max-height: 240px;
  overflow-y: auto;
  padding: 8px;
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
  gap: 4px;
}

.icon-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 4px;
  padding: 8px 4px;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.2s ease;
  text-align: center;
}

.icon-item:hover {
  background: #f3f4f6;
}

.icon-item.active {
  background: #dbeafe;
  color: #3b82f6;
}

.icon-item i {
  font-size: 16px;
}

.icon-name {
  font-size: 11px;
  color: #6b7280;
  line-height: 1.2;
  word-break: break-all;
}

.icon-item.active .icon-name {
  color: #3b82f6;
}

/* 移动端适配 */
@media (max-width: 768px) {
  .stats-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 6px; /* 进一步减小间距 */
    margin-bottom: 6px; /* 进一步减小下边距 */
  }

  .stat-card {
    padding: 6px; /* 进一步减小内边距 */
    min-height: 60px; /* 减小移动端最小高度 */
  }

  .stat-label {
    font-size: 10px; /* 进一步减小字体 */
  }

  .stat-value {
    font-size: 14px; /* 进一步减小数值字体 */
  }

  .stat-icon {
    width: 18px; /* 进一步减小图标 */
    height: 18px; /* 进一步减小图标 */
    font-size: 9px; /* 进一步减小图标字体 */
  }
  
  .card-header {
    padding: 8px 12px;
  }
  
  .card-title {
    font-size: 13px;
  }
  
  /* 移动端表格适配 */
  .standard-table .el-table th,
  .standard-table .el-table td {
    padding: 4px 6px;
  }
  
  .mqtt-management .cell {
    padding: 2px 4px;
  }
  
  .mqtt-management .el-tag {
    padding: 1px 4px;
    font-size: 10px;
  }
  
  .mqtt-management .el-button--small {
    padding: 2px 6px;
    font-size: 10px;
  }
}

/* ===== 现代化设计系统 ===== */
/* 所有样式由 modern-design.css 统一管理 */
</style>

<!-- 非scoped样式，用于穿透组件作用域 -->
<style>
/* 表格选择列对齐 - 复选框居中 */
.modern-table-container .el-table__body .el-table__row .el-table-column--selection,
.modern-table-container .el-table__header .el-table__row .el-table-column--selection {
  vertical-align: middle !important;
  padding: 16px 12px !important;
  text-align: center !important;
}

.modern-table-container .el-table__body .el-table__row .el-table-column--selection .cell,
.modern-table-container .el-table__header .el-table__row .el-table-column--selection .cell {
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  padding: 0 !important;
}

.modern-table-container .el-table__body .el-table__row .el-table-column--selection .el-checkbox,
.modern-table-container .el-table__header .el-table__row .el-table-column--selection .el-checkbox {
  margin: 0 !important;
}

/* 确保表头wrapper也居中 */
.modern-table-container .el-table__header-wrapper .el-table__header .el-table-column--selection .cell {
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
}

/* 统计图标颜色 - 现代化渐变风格 */
.stat-icon.success {
  background: linear-gradient(135deg, #52c41a, #73d13d) !important;
  color: white !important;
}

.stat-icon.danger {
  background: linear-gradient(135deg, #ff4d4f, #ff7875) !important;
  color: white !important;
}

.stat-icon.primary {
  background: linear-gradient(135deg, #1890ff, #40a9ff) !important;
  color: white !important;
}

.stat-icon.warning {
  background: linear-gradient(135deg, #faad14, #ffc53d) !important;
  color: white !important;
}

.stat-icon.info {
  background: linear-gradient(135deg, #667eea, #764ba2) !important;
  color: white !important;
}

/* 实时更新动画 */
@keyframes pulse {
  0% { opacity: 1; }
  50% { opacity: 0.5; }
  100% { opacity: 1; }
}

.card-actions {
  display: flex;
  gap: 8px;
  align-items: center;
}

.modern-btn.success {
  background: linear-gradient(135deg, #10b981, #047857);
  color: white !important;
  border: none;
}

.modern-btn.success:hover {
  background: linear-gradient(135deg, #73d13d, #95de64);
  transform: translateY(-1px);
}

.modern-btn.warning {
  background: linear-gradient(135deg, #faad14, #ffc53d);
  color: white;
  border: none;
}

.modern-btn.warning:hover {
  background: linear-gradient(135deg, #ffc53d, #ffd666);
  transform: translateY(-1px);
}

.form-hint {
  font-size: 12px;
  color: #909399;
  margin-top: 4px;
  font-style: italic;
}

/* 心跳监控样式 */
.heartbeat-monitor {
  padding: 20px 0;
}

.monitor-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding-bottom: 15px;
  border-bottom: 1px solid #ebeef5;
}

.client-info {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 16px;
  font-weight: 600;
  color: #303133;
}

.client-info i {
  color: #409eff;
  font-size: 18px;
}

.monitor-status {
  display: flex;
  align-items: center;
}

.heartbeat-chart {
  background: #fafafa;
  border: 1px solid #e4e7ed;
  border-radius: 8px;
  padding: 15px;
  margin-bottom: 20px;
  position: relative;
}

.heartbeat-canvas {
  display: block;
  width: 100%;
  background: #fff;
  border-radius: 4px;
  box-shadow: 0 1px 3px rgba(0,0,0,0.1);
}

.heartbeat-stats {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 20px;
  padding: 15px;
  background: #f8f9fa;
  border-radius: 8px;
}

.stat-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
}

.stat-label {
  font-size: 12px;
  color: #909399;
  margin-bottom: 5px;
}

.stat-value {
  font-size: 18px;
  font-weight: 600;
  color: #303133;
}

/* 心跳动画效果 */
@keyframes heartbeat-pulse {
  0% { transform: scale(1); }
  50% { transform: scale(1.1); }
  100% { transform: scale(1); }
}

.heartbeat-active .client-info i {
  animation: heartbeat-pulse 1s ease-in-out infinite;
}

/* 心跳监控响应式设计 */
@media (max-width: 768px) {
  .heartbeat-stats {
    grid-template-columns: 1fr;
    gap: 15px;
  }

  .heartbeat-canvas {
    height: 150px;
  }

  .monitor-header {
    flex-direction: column;
    gap: 10px;
    align-items: flex-start;
  }
}

/* 系统设置样式 */
.settings-management {
  padding: 0;
}

.settings-tabs {
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.settings-tabs .el-tabs__header {
  margin: 0;
  padding: 0 20px;
  background: #f8f9fa;
  border-bottom: 1px solid #e4e7ed;
  border-radius: 8px 8px 0 0;
}

.settings-tabs .el-tabs__content {
  padding: 0;
}

.settings-tabs .el-tab-pane {
  padding: 20px;
}

.security-form {
  max-width: 800px;
}

.security-form .el-form-item {
  margin-bottom: 24px;
}

.security-form .el-form-item__label {
  font-weight: 600;
  color: #303133;
}

.security-form .el-input,
.security-form .el-input-number {
  width: 100%;
}

.security-form .el-textarea {
  width: 100%;
}

.security-form .el-switch {
  margin-right: 12px;
}

.form-help {
  margin-top: 8px;
  font-size: 12px;
  color: #909399;
  line-height: 1.4;
  display: block;
  width: 100%;
  clear: both;
}

.card-actions {
  display: flex;
  gap: 12px;
  align-items: center;
}

.card-actions .modern-btn {
  padding: 8px 16px;
  font-size: 14px;
  border-radius: 6px;
  border: 1px solid;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 6px;
}

.card-actions .modern-btn.primary {
  background: #409eff;
  border-color: #409eff;
  color: white;
}

.card-actions .modern-btn.primary:hover {
  background: #66b1ff;
  border-color: #66b1ff;
}

.card-actions .modern-btn.secondary {
  background: #606266;
  border-color: #606266;
  color: white;
}

.card-actions .modern-btn.secondary:hover {
  background: #73767a;
  border-color: #73767a;
  color: white;
}

.card-actions .modern-btn.warning {
  background: #e6a23c;
  border-color: #e6a23c;
  color: white;
}

.card-actions .modern-btn.warning:hover {
  background: #ebb563;
  border-color: #ebb563;
}

.card-actions .modern-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.card-actions .modern-btn i {
  font-size: 14px;
}

/* 系统设置响应式设计 */
@media (max-width: 768px) {
  .settings-tabs .el-tabs__header {
    padding: 0 15px;
  }

  .settings-tabs .el-tab-pane {
    padding: 15px;
  }

  .security-form {
    max-width: 100%;
  }

  .card-actions {
    flex-direction: column;
    align-items: stretch;
  }

  .card-actions .modern-btn {
    justify-content: center;
  }
}

/* 备份恢复样式 */
.backup-section {
  margin-bottom: 32px;
  padding-bottom: 24px;
  border-bottom: 1px solid #f0f0f0;
}

.backup-section:last-child {
  border-bottom: none;
  margin-bottom: 0;
}

.section-subtitle {
  font-size: 16px;
  font-weight: 600;
  color: #303133;
  margin-bottom: 16px;
  display: flex;
  align-items: center;
  gap: 8px;
}

.backup-form,
.auto-backup-form {
  max-width: 600px;
}

.backup-filename {
  display: flex;
  align-items: center;
  gap: 8px;
  font-family: 'Courier New', monospace;
}

.backup-filename i {
  color: #909399;
}

.backup-actions {
  display: flex;
  gap: 8px;
}

.action-btn {
  width: 32px;
  height: 32px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
}

.action-btn.download {
  background: #e1f3d8;
  color: #67c23a;
}

.action-btn.download:hover {
  background: #67c23a;
  color: white;
}

.action-btn.restore {
  background: #ecf5ff;
  color: #409eff;
}

.action-btn.restore:hover {
  background: #409eff;
  color: white;
}

.action-btn.delete {
  background: #fef0f0;
  color: #f56c6c;
}

.action-btn.delete:hover {
  background: #f56c6c;
  color: white;
}

.empty-backup {
  text-align: center;
  padding: 40px 20px;
  color: #909399;
}

.empty-backup i {
  font-size: 48px;
  margin-bottom: 16px;
  display: block;
}

.empty-backup p {
  margin: 8px 0;
}

.empty-tip {
  font-size: 14px;
  color: #c0c4cc;
}

/* 备份恢复响应式设计 */
@media (max-width: 768px) {
  .backup-form,
  .auto-backup-form {
    max-width: 100%;
  }

  .backup-actions {
    flex-direction: column;
    gap: 4px;
  }

  .action-btn {
    width: 28px;
    height: 28px;
  }

  .section-subtitle {
    font-size: 14px;
  }
}

/* 简洁的配置提示样式 */
.config-help {
  margin-top: 8px;
  font-size: 12px;
  color: #666;
  line-height: 1.4;
  width: 100%;
  clear: both;
}

.config-help .range {
  color: #409eff;
  font-weight: 500;
  display: block;
}

.config-help .desc {
  margin-top: 4px;
  color: #999;
  display: block;
}

/* 确保输入框容器正确布局 */
.input-container {
  display: block;
  width: 100%;
}

.input-container .el-input-number {
  margin-right: 10px;
}

/* 证书管理样式 */
.certificate-warnings {
  margin-bottom: 20px;
}

.certificate-stats {
  margin-bottom: 20px;
}

.stat-card {
  background: #f8f9fa;
  border-radius: 8px;
  padding: 20px;
  text-align: center;
  border: 1px solid #e9ecef;
  transition: all 0.3s ease;
}

.stat-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.stat-number {
  font-size: 28px;
  font-weight: bold;
  color: #409eff;
  margin-bottom: 8px;
}

.stat-label {
  font-size: 14px;
  color: #666;
  font-weight: 500;
}

.certificate-list {
  margin-top: 20px;
}

.certificate-name {
  display: flex;
  align-items: center;
  font-weight: 500;
}

.certificate-name i {
  color: #409eff;
}

/* 证书表格样式优化 */
.certificate-list .el-table {
  border-radius: 8px;
  overflow: hidden;
}

.certificate-list .el-table th {
  background-color: #f8f9fa;
  color: #333;
  font-weight: 600;
}

.certificate-list .el-table td {
  padding: 12px 0;
}

/* 证书状态颜色 */
.cert-status-valid {
  color: #67c23a;
}

.cert-status-expiring {
  color: #e6a23c;
}

.cert-status-expired {
  color: #f56c6c;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .certificate-stats .el-col {
    margin-bottom: 10px;
  }

  .stat-card {
    padding: 15px;
  }

  .stat-number {
    font-size: 24px;
  }
}

/* 授权配置界面样式 */
.authorization-config {
  padding: 12px;
  background: #f8fafc;
  max-width: 800px;
  margin: 0 auto;
}

.form-group {
  margin-bottom: 16px;
}

.form-label {
  display: flex;
  align-items: center;
  gap: 8px;
  font-weight: 600;
  color: #374151;
  margin-bottom: 8px;
  font-size: 14px;
}

.form-label i {
  color: #6366f1;
  font-size: 14px;
}

.form-tip {
  font-size: 12px;
  color: #6b7280;
  margin-top: 4px;
  line-height: 1.3;
  padding-left: 4px;
}

.switch-group {
  margin-top: 8px;
  padding: 12px;
  background: #f9fafb;
  border-radius: 8px;
  border: 1px solid #e5e7eb;
}

.switch-item {
  display: flex;
  flex-direction: column;
  gap: 6px;
  height: 100%;
}

.switch-tip {
  font-size: 12px;
  color: #6b7280;
  margin-left: 4px;
}

.checkbox-grid {
  background: #f9fafb;
  border-radius: 8px;
  padding: 12px;
  border: 1px solid #e5e7eb;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.checkbox-row {
  display: flex;
  justify-content: center;
  gap: 16px;
  margin-bottom: 8px;
  width: 100%;
}

.checkbox-row:last-child {
  margin-bottom: 0;
}

.checkbox-item {
  padding: 8px 12px;
  background: white;
  border-radius: 6px;
  border: 1px solid #e5e7eb;
  transition: all 0.2s ease;
  text-align: center;
  width: 150px;
  flex-shrink: 0;
}

.checkbox-item .el-checkbox {
  display: flex !important;
  justify-content: center !important;
  align-items: center !important;
  width: 100% !important;
}

.checkbox-item .el-checkbox .el-checkbox__input {
  margin: 0 !important;
}

.checkbox-item .el-checkbox .el-checkbox__label {
  margin-left: 8px !important;
  display: flex !important;
  align-items: center !important;
  gap: 6px !important;
  font-size: 14px !important;
}

.checkbox-item:hover {
  border-color: #6366f1;
  background: #f0f9ff;
}

.checkbox-item i {
  color: #6366f1;
  font-size: 14px;
}

.authorization-config .el-switch {
  --el-switch-on-color: #10b981;
  --el-switch-off-color: #6b7280;
}

.authorization-config .el-input-number {
  --el-input-border-color: #d1d5db;
  --el-input-focus-border-color: #6366f1;
}

.authorization-config .el-date-editor {
  --el-input-border-color: #d1d5db;
  --el-input-focus-border-color: #6366f1;
}

.authorization-config .el-checkbox {
  --el-checkbox-checked-bg-color: #6366f1;
  --el-checkbox-checked-border-color: #6366f1;
}

@media (max-width: 768px) {
  .authorization-config {
    padding: 8px;
    max-width: none;
  }

  .switch-group {
    padding: 8px;
  }

  .switch-group .el-row {
    flex-direction: column;
  }

  .switch-group .el-col {
    margin-bottom: 12px;
  }

  .checkbox-grid {
    padding: 8px;
  }

  .checkbox-row {
    flex-direction: column;
    gap: 8px;
  }

  .checkbox-item {
    min-width: auto;
    width: 100%;
    padding: 6px 8px;
  }

  .form-group {
    margin-bottom: 12px;
  }
}
</style>